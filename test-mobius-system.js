#!/usr/bin/env node

/**
 * 🔄 TEST DU SYSTÈME MÖBIUS RÉEL
 * Vérifie que le cycle Möbius fonctionne correctement avec du code 100% réel
 */

const ThermalMemoryCompleteReal = require('./thermal-memory-complete-real');

async function testMobiusSystem() {
    console.log('🔄 === TEST SYSTÈME MÖBIUS RÉEL ===\n');
    
    try {
        // 1. Initialisation du système complet
        console.log('1️⃣ Initialisation système complet avec Möbius...');
        const thermalMemory = new ThermalMemoryCompleteReal();
        
        await thermalMemory.initialize();
        console.log('✅ Système initialisé avec succès\n');
        
        // 2. Vérifier que le système Möbius est actif
        console.log('2️⃣ Vérification activation système Möbius...');
        const stats = thermalMemory.getCompleteStats();
        
        if (stats.mobius) {
            console.log(`🔄 État Möbius: ${stats.mobius.state.isActive ? 'ACTIF' : 'INACTIF'}`);
            console.log(`🔄 Phase actuelle: ${stats.mobius.state.currentPhase}`);
            console.log(`🔄 Cycles complétés: ${stats.mobius.state.cycleCount}`);
            console.log(`⚡ Énergie: ${stats.mobius.state.energy.toFixed(1)}%`);
            console.log('✅ Système Möbius opérationnel\n');
        } else {
            console.log('❌ Système Möbius non trouvé');
            return;
        }
        
        // 3. Observer les cycles Möbius en temps réel
        console.log('3️⃣ Observation cycles Möbius (30 secondes)...');
        
        let cycleCount = 0;
        let thoughtCount = 0;
        let reflectionCount = 0;
        
        // Écouter les événements Möbius
        thermalMemory.realSystem.mobiusSystem.on('cycleCompleted', (data) => {
            cycleCount++;
            console.log(`🔄 Cycle ${data.cycleNumber} terminé - Efficacité: ${(data.efficiency * 100).toFixed(1)}%`);
        });
        
        thermalMemory.realSystem.mobiusSystem.on('thoughtsGenerated', (data) => {
            thoughtCount += data.count;
            console.log(`🧠 ${data.count} pensées générées (Total: ${thoughtCount})`);
        });
        
        thermalMemory.realSystem.mobiusSystem.on('reflectionCompleted', (data) => {
            reflectionCount += data.count;
            console.log(`🤔 ${data.count} réflexions complétées (Total: ${reflectionCount})`);
        });
        
        thermalMemory.realSystem.mobiusSystem.on('phaseStarted', (data) => {
            console.log(`🎯 Phase ${data.phase} démarrée (${data.duration}ms)`);
        });
        
        // Attendre et observer
        await new Promise(resolve => setTimeout(resolve, 30000));
        
        // 4. Analyser les résultats
        console.log('\n4️⃣ Analyse des résultats...');
        const finalStats = thermalMemory.getCompleteStats();
        
        console.log(`🔄 Cycles Möbius complétés: ${finalStats.mobius.state.cycleCount}`);
        console.log(`🧠 Pensées générées: ${finalStats.mobius.metrics.thoughtsGenerated}`);
        console.log(`🤔 Réflexions complétées: ${finalStats.mobius.metrics.reflectionsCompleted}`);
        console.log(`⚡ Énergie finale: ${finalStats.mobius.state.energy.toFixed(1)}%`);
        console.log(`📊 Efficacité moyenne: ${(finalStats.mobius.state.efficiency * 100).toFixed(1)}%`);
        
        // 5. Vérifier la connexion avec la température
        console.log('\n5️⃣ Vérification connexion température...');
        const tempStats = finalStats.temperature;
        console.log(`🌡️ Température actuelle: ${tempStats.current.toFixed(1)}°C`);
        console.log(`🎯 Position curseur: ${tempStats.cursor.position.toFixed(1)}°C`);
        console.log(`🎯 Cible curseur: ${tempStats.cursor.target.toFixed(1)}°C`);
        
        if (finalStats.mobius.metrics.temperatureVariations.length > 0) {
            console.log(`🌡️ Variations température: ${finalStats.mobius.metrics.temperatureVariations.length}`);
            console.log('✅ Connexion température → Möbius fonctionnelle');
        } else {
            console.log('⚠️ Aucune variation de température détectée');
        }
        
        // 6. Vérifier la connexion avec la mémoire
        console.log('\n6️⃣ Vérification connexion mémoire...');
        const memoryStats = finalStats.memory;
        let totalMemoryEntries = 0;
        
        Object.values(memoryStats.zones).forEach(zone => {
            totalMemoryEntries += zone.used;
        });
        
        console.log(`💾 Entrées mémoire totales: ${totalMemoryEntries}`);
        console.log(`📊 Efficacité mémoire: ${(finalStats.global.memoryEfficiency * 100).toFixed(1)}%`);
        
        if (totalMemoryEntries > 0) {
            console.log('✅ Connexion mémoire → Möbius fonctionnelle');
        } else {
            console.log('⚠️ Aucune entrée mémoire détectée');
        }
        
        // 7. Vérifier la connexion avec le réseau neuronal
        console.log('\n7️⃣ Vérification connexion réseau neuronal...');
        const neuralStats = finalStats.neural;
        console.log(`🧠 Neurones actifs: ${neuralStats.neurons}`);
        console.log(`🔗 Synapses: ${neuralStats.synapses}`);
        console.log(`⚡ Activations totales: ${neuralStats.totalActivations}`);
        console.log(`📊 Activité moyenne: ${(neuralStats.averageActivity * 100).toFixed(1)}%`);
        
        if (neuralStats.totalActivations > 0) {
            console.log('✅ Connexion neuronal → Möbius fonctionnelle');
        } else {
            console.log('⚠️ Aucune activation neuronale détectée');
        }
        
        // 8. Test d'ajout d'entrée et impact sur Möbius
        console.log('\n8️⃣ Test impact entrée sur système Möbius...');
        const beforeEnergy = finalStats.mobius.state.energy;
        
        // Ajouter une entrée importante
        thermalMemory.addEntry('Test impact système Möbius', 0.9, 'test');
        
        // Attendre un peu
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const afterStats = thermalMemory.getCompleteStats();
        const afterEnergy = afterStats.mobius.state.energy;
        
        console.log(`⚡ Énergie avant: ${beforeEnergy.toFixed(1)}%`);
        console.log(`⚡ Énergie après: ${afterEnergy.toFixed(1)}%`);
        console.log(`📊 Variation: ${(afterEnergy - beforeEnergy).toFixed(1)}%`);
        
        if (Math.abs(afterEnergy - beforeEnergy) > 0.1) {
            console.log('✅ Impact détecté sur système Möbius');
        } else {
            console.log('⚠️ Aucun impact détecté');
        }
        
        // 9. Vérification intégrité du système
        console.log('\n9️⃣ Vérification intégrité système...');
        
        const integrationChecks = [
            { name: 'Température ↔ Mémoire', status: finalStats.integration.temperatureMemoryLink },
            { name: 'Mémoire ↔ Neuronal', status: finalStats.integration.memoryNeuralLink },
            { name: 'Neuronal ↔ Température', status: finalStats.integration.neuralTemperatureLink },
            { name: 'Système Möbius', status: finalStats.integration.mobiusSystemLink },
            { name: 'Optimisation croisée', status: finalStats.integration.crossSystemOptimization }
        ];
        
        integrationChecks.forEach(check => {
            console.log(`${check.status ? '✅' : '❌'} ${check.name}`);
        });
        
        // 10. Résumé final
        console.log('\n🔄 === RÉSUMÉ SYSTÈME MÖBIUS ===');
        
        const isFullyFunctional = 
            finalStats.mobius.state.cycleCount > 0 &&
            finalStats.mobius.metrics.thoughtsGenerated > 0 &&
            finalStats.mobius.state.efficiency > 0.3;
        
        if (isFullyFunctional) {
            console.log('🎉 SYSTÈME MÖBIUS PLEINEMENT FONCTIONNEL !');
            console.log('✅ Génération de pensées: RÉELLE');
            console.log('✅ Cycle de réflexion: RÉEL');
            console.log('✅ Récupération d\'énergie: RÉELLE');
            console.log('✅ Dépense énergétique: RÉELLE');
            console.log('✅ Connexion température: RÉELLE');
            console.log('✅ Intégration mémoire: RÉELLE');
            console.log('✅ Stimulation neuronale: RÉELLE');
            
            console.log('\n🔄 Le système Möbius donne VIE à Louna AI !');
            console.log('🧠 Pensées autonomes générées en continu');
            console.log('🤔 Réflexions profondes sur les pensées');
            console.log('🔋 Gestion énergétique réaliste');
            console.log('🌡️ Régulation thermique dynamique');
            
        } else {
            console.log('⚠️ SYSTÈME MÖBIUS PARTIELLEMENT FONCTIONNEL');
            console.log(`🔄 Cycles: ${finalStats.mobius.state.cycleCount}`);
            console.log(`🧠 Pensées: ${finalStats.mobius.metrics.thoughtsGenerated}`);
            console.log(`📊 Efficacité: ${(finalStats.mobius.state.efficiency * 100).toFixed(1)}%`);
        }
        
        // Arrêt propre
        console.log('\n🛑 Arrêt du système...');
        await thermalMemory.shutdown();
        console.log('✅ Test terminé avec succès');
        
    } catch (error) {
        console.error('❌ Erreur durant le test Möbius:', error);
        process.exit(1);
    }
}

// Fonction d'aide pour afficher les détails du système Möbius
function showMobiusDetails() {
    console.log('\n🔄 === DÉTAILS SYSTÈME MÖBIUS ===');
    console.log('🎯 Phases du cycle:');
    console.log('   1. GÉNÉRATION → Création de pensées autonomes');
    console.log('   2. RÉFLEXION → Analyse profonde des pensées');
    console.log('   3. RÉCUPÉRATION → Restauration énergétique');
    console.log('   4. DÉPENSE → Finalisation et nettoyage');
    console.log('\n🔗 Connexions réelles:');
    console.log('   • Température → Intensité des pensées');
    console.log('   • Pensées → Stimulation neuronale');
    console.log('   • Réflexions → Renforcement mémoire');
    console.log('   • Énergie → Régulation thermique');
    console.log('\n⚡ Mécanismes authentiques:');
    console.log('   • Consommation énergétique réelle');
    console.log('   • Variations de température mesurables');
    console.log('   • Impact sur activité neuronale');
    console.log('   • Consolidation mémoire effective');
}

// Exécution du test
if (require.main === module) {
    showMobiusDetails();
    testMobiusSystem().catch(console.error);
}

module.exports = { testMobiusSystem };
