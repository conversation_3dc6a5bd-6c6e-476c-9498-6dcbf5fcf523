const axios = require('axios');
const EventEmitter = require('events');

/**
 * 🧠 CONNECTEUR DIRECT DEEPSEEK R1 8B → MÉMOIRE THERMIQUE
 * Connexion directe sans Ollama pour performance maximale
 */
class DeepSeekDirectConnector extends EventEmitter {
    constructor(thermalMemory, config = {}) {
        super();
        
        this.thermalMemory = thermalMemory;
        
        // 🔑 CONFIGURATION DEEPSEEK API
        this.config = {
            apiKey: config.apiKey || process.env.DEEPSEEK_API_KEY || '***********************************',
            apiUrl: config.apiUrl || 'https://api.deepseek.com',
            model: config.model || 'deepseek-chat', // Modèle fonctionnel
            maxTokens: config.maxTokens || 2048,
            temperature: config.temperature || 0.7,
            timeout: config.timeout || 30000
        };
        
        // 📊 MÉTRIQUES DE PERFORMANCE
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            totalTokensUsed: 0,
            memoryIntegrations: 0,
            lastRequestTime: null,
            uptime: Date.now()
        };
        
        // 🧠 CACHE INTELLIGENT
        this.cache = new Map();
        this.cacheMaxSize = 100;
        this.cacheTimeout = 300000; // 5 minutes
        
        // 🔄 INTÉGRATION MÖBIUS
        this.mobiusIntegration = {
            enabled: true,
            thoughtsFromDeepSeek: 0,
            reflectionsToDeepSeek: 0,
            lastMobiusSync: Date.now()
        };
        
        console.log('🧠 Connecteur Direct DeepSeek R1 8B initialisé');
        console.log(`🔑 API Key: ${this.config.apiKey ? 'CONFIGURÉE' : 'MANQUANTE'}`);
        console.log(`🌐 API URL: ${this.config.apiUrl}`);
        console.log(`🤖 Modèle: ${this.config.model}`);
        
        this.initialize();
    }

    /**
     * 🚀 Initialise le connecteur
     */
    async initialize() {
        try {
            // Tester la connexion API
            await this.testConnection();
            
            // Connecter aux événements de la mémoire thermique
            this.connectToThermalMemory();
            
            // Connecter au système Möbius si disponible
            this.connectToMobiusSystem();
            
            console.log('✅ Connecteur DeepSeek Direct initialisé avec succès');
            this.emit('initialized');
            
        } catch (error) {
            console.error('❌ Erreur initialisation connecteur DeepSeek:', error.message);
            this.emit('error', error);
        }
    }

    /**
     * 🔍 Teste la connexion à l'API DeepSeek
     */
    async testConnection() {
        try {
            const response = await this.makeRequest('Test de connexion', [], {
                max_tokens: 10,
                temperature: 0.1
            });
            
            if (response.success) {
                console.log('✅ Connexion DeepSeek API validée');
                return true;
            } else {
                throw new Error('Test de connexion échoué');
            }
        } catch (error) {
            console.error('❌ Test connexion DeepSeek échoué:', error.message);
            throw error;
        }
    }

    /**
     * 🔗 Connecte aux événements de la mémoire thermique
     */
    connectToThermalMemory() {
        if (!this.thermalMemory) return;

        // Écouter les nouvelles entrées importantes
        this.thermalMemory.on('entryAdded', (entry) => {
            if (entry.importance > 0.8) {
                this.processHighImportanceEntry(entry);
            }
        });

        // Écouter les changements de température
        this.thermalMemory.on('temperatureUpdate', (data) => {
            this.adjustResponseBasedOnTemperature(data.temperature);
        });

        console.log('🔗 Connecté aux événements mémoire thermique');
    }

    /**
     * 🔄 Connecte au système Möbius
     */
    connectToMobiusSystem() {
        if (!this.thermalMemory || !this.thermalMemory.mobiusState) return;

        // Écouter les pensées générées par Möbius
        this.thermalMemory.on('mobiusThoughtsGenerated', (data) => {
            this.processMobiusThoughts(data.thoughts);
        });

        // Écouter les cycles complétés
        this.thermalMemory.on('mobiusCycleCompleted', (data) => {
            this.syncWithMobiusCycle(data);
        });

        console.log('🔄 Connecté au système Möbius');
    }

    /**
     * 💬 Envoie une requête directe à DeepSeek R1 8B
     */
    async chat(message, context = {}) {
        try {
            const startTime = Date.now();
            
            // Enrichir le message avec le contexte de la mémoire thermique
            const enrichedMessage = await this.enrichWithThermalMemory(message, context);
            
            // Préparer l'historique
            const history = context.history || [];
            
            // Faire la requête
            const response = await this.makeRequest(enrichedMessage, history, {
                temperature: context.temperature || this.config.temperature,
                max_tokens: context.maxTokens || this.config.maxTokens
            });
            
            if (response.success) {
                // Stocker la réponse dans la mémoire thermique
                await this.storeInThermalMemory(message, response.content, context);
                
                // Mettre à jour les métriques
                this.updateMetrics(startTime, response);
                
                // Intégrer avec Möbius si activé
                if (this.mobiusIntegration.enabled) {
                    this.integrateWithMobius(response.content);
                }
                
                console.log(`🧠 DeepSeek R1 réponse: ${response.content.substring(0, 100)}...`);
                
                return {
                    success: true,
                    content: response.content,
                    model: this.config.model,
                    tokensUsed: response.tokensUsed || 0,
                    responseTime: Date.now() - startTime,
                    thermalIntegration: true,
                    mobiusIntegration: this.mobiusIntegration.enabled
                };
            } else {
                throw new Error(response.error || 'Erreur inconnue');
            }
            
        } catch (error) {
            console.error('❌ Erreur chat DeepSeek:', error.message);
            this.metrics.failedRequests++;
            
            return {
                success: false,
                error: error.message,
                model: this.config.model
            };
        }
    }

    /**
     * 🌐 Fait une requête HTTP à l'API DeepSeek
     */
    async makeRequest(message, history = [], options = {}) {
        try {
            this.metrics.totalRequests++;
            this.metrics.lastRequestTime = Date.now();
            
            // Vérifier le cache
            const cacheKey = this.generateCacheKey(message, history);
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                console.log('📦 Réponse depuis cache DeepSeek');
                return cached;
            }
            
            // Préparer les messages
            const messages = [
                ...history,
                { role: 'user', content: message }
            ];
            
            // Préparer la requête
            const requestData = {
                model: this.config.model,
                messages: messages,
                temperature: options.temperature || this.config.temperature,
                max_tokens: options.max_tokens || this.config.maxTokens,
                stream: false
            };
            
            // Faire la requête
            const response = await axios.post(
                `${this.config.apiUrl}/v1/chat/completions`,
                requestData,
                {
                    headers: {
                        'Authorization': `Bearer ${this.config.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: this.config.timeout
                }
            );
            
            if (response.data && response.data.choices && response.data.choices[0]) {
                const result = {
                    success: true,
                    content: response.data.choices[0].message.content,
                    tokensUsed: response.data.usage?.total_tokens || 0,
                    model: response.data.model || this.config.model
                };
                
                // Mettre en cache
                this.setCache(cacheKey, result);
                
                this.metrics.successfulRequests++;
                this.metrics.totalTokensUsed += result.tokensUsed;
                
                return result;
            } else {
                throw new Error('Réponse API invalide');
            }
            
        } catch (error) {
            console.error('❌ Erreur requête DeepSeek API:', error.message);
            
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🧠 Enrichit le message avec le contexte de la mémoire thermique
     */
    async enrichWithThermalMemory(message, context = {}) {
        if (!this.thermalMemory) return message;

        try {
            // Rechercher des souvenirs pertinents
            const relevantMemories = this.thermalMemory.search(message, { limit: 3 });
            
            if (relevantMemories.length > 0) {
                const memoryContext = relevantMemories.map(memory => 
                    `[Mémoire ${memory.zone || 'thermique'}] ${memory.data || memory.content}`
                ).join('\n');
                
                const enrichedMessage = `Contexte de mémoire thermique:\n${memoryContext}\n\nQuestion: ${message}`;
                
                console.log(`🧠 Message enrichi avec ${relevantMemories.length} souvenirs thermiques`);
                
                return enrichedMessage;
            }
            
        } catch (error) {
            console.warn('⚠️ Erreur enrichissement mémoire thermique:', error.message);
        }
        
        return message;
    }

    /**
     * 💾 Stocke la conversation dans la mémoire thermique
     */
    async storeInThermalMemory(question, answer, context = {}) {
        if (!this.thermalMemory) return;

        try {
            // Stocker la question
            this.thermalMemory.add(
                `Question DeepSeek: ${question}`,
                context.importance || 0.7,
                'deepseek_question'
            );
            
            // Stocker la réponse
            this.thermalMemory.add(
                `Réponse DeepSeek R1: ${answer}`,
                context.importance || 0.8,
                'deepseek_response'
            );
            
            this.metrics.memoryIntegrations++;
            
            console.log('💾 Conversation stockée dans mémoire thermique');
            
        } catch (error) {
            console.warn('⚠️ Erreur stockage mémoire thermique:', error.message);
        }
    }

    /**
     * 🔄 Intègre avec le système Möbius
     */
    integrateWithMobius(content) {
        if (!this.thermalMemory || !this.thermalMemory.mobiusState) return;

        try {
            // Créer une "pensée" basée sur la réponse DeepSeek
            const mobiusThought = {
                id: `deepseek_thought_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                type: 'deepseek_integration',
                content: `Intégration DeepSeek: ${content.substring(0, 200)}...`,
                intensity: 0.9,
                timestamp: Date.now(),
                temperature: this.thermalMemory.memory.temperature,
                source: 'deepseek_r1'
            };
            
            // Ajouter à la queue de pensées Möbius
            if (this.thermalMemory.thoughtQueue) {
                this.thermalMemory.thoughtQueue.push(mobiusThought);
            }
            
            this.mobiusIntegration.thoughtsFromDeepSeek++;
            this.mobiusIntegration.lastMobiusSync = Date.now();
            
            console.log('🔄 Intégration Möbius ← DeepSeek complétée');
            
        } catch (error) {
            console.warn('⚠️ Erreur intégration Möbius:', error.message);
        }
    }

    /**
     * 🧠 Traite les entrées importantes de la mémoire thermique
     */
    async processHighImportanceEntry(entry) {
        try {
            // Analyser l'entrée avec DeepSeek
            const analysis = await this.chat(
                `Analyse cette information importante: ${entry.data}`,
                { 
                    importance: 0.9,
                    temperature: 0.3,
                    maxTokens: 512
                }
            );
            
            if (analysis.success) {
                console.log(`🧠 Analyse DeepSeek d'entrée importante: ${analysis.content.substring(0, 100)}...`);
            }
            
        } catch (error) {
            console.warn('⚠️ Erreur analyse entrée importante:', error.message);
        }
    }

    /**
     * 🌡️ Ajuste les réponses basées sur la température
     */
    adjustResponseBasedOnTemperature(temperature) {
        // Ajuster la température de l'IA basée sur la température thermique
        if (temperature > 40) {
            this.config.temperature = Math.min(1.0, this.config.temperature + 0.1);
        } else if (temperature < 35) {
            this.config.temperature = Math.max(0.1, this.config.temperature - 0.1);
        }
        
        console.log(`🌡️ Température DeepSeek ajustée: ${this.config.temperature.toFixed(2)} (Thermique: ${temperature.toFixed(1)}°C)`);
    }

    /**
     * 📦 Gestion du cache
     */
    generateCacheKey(message, history) {
        const historyStr = history.map(h => h.content).join('|');
        return `${message}_${historyStr}`.substring(0, 100);
    }

    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }

    setCache(key, data) {
        if (this.cache.size >= this.cacheMaxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 📊 Met à jour les métriques
     */
    updateMetrics(startTime, response) {
        const responseTime = Date.now() - startTime;
        this.metrics.averageResponseTime = 
            (this.metrics.averageResponseTime + responseTime) / 2;
    }

    /**
     * 📊 Obtient les statistiques du connecteur
     */
    getStats() {
        return {
            config: {
                model: this.config.model,
                apiUrl: this.config.apiUrl,
                hasApiKey: !!this.config.apiKey
            },
            metrics: this.metrics,
            cache: {
                size: this.cache.size,
                maxSize: this.cacheMaxSize
            },
            mobiusIntegration: this.mobiusIntegration,
            uptime: Date.now() - this.metrics.uptime,
            status: 'active'
        };
    }

    /**
     * 🛑 Arrête le connecteur
     */
    stop() {
        this.cache.clear();
        console.log('🛑 Connecteur DeepSeek Direct arrêté');
        this.emit('stopped');
    }
}

module.exports = DeepSeekDirectConnector;
