const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');

/**
 * 💾 MÉMOIRE THERMIQUE SIMPLIFIÉE
 * Module de base pour la mémoire thermique
 */
class ThermalMemory extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            dataPath: config.dataPath || path.join(__dirname, '../data/memory'),
            maxMemories: config.maxMemories || 1000,
            compressionThreshold: config.compressionThreshold || 100
        };
        
        this.memories = new Map();
        this.stats = {
            totalMemories: 0,
            totalSize: 0,
            lastAccess: Date.now()
        };
        
        this.initialize();
    }
    
    /**
     * 🚀 Initialise la mémoire thermique
     */
    initialize() {
        try {
            // Créer le répertoire de données si nécessaire
            if (!fs.existsSync(this.config.dataPath)) {
                fs.mkdirSync(this.config.dataPath, { recursive: true });
            }
            
            console.log('💾 Mémoire thermique simplifiée initialisée');
            this.emit('initialized');
            
        } catch (error) {
            console.error('❌ Erreur initialisation mémoire thermique:', error.message);
            this.emit('error', error);
        }
    }
    
    /**
     * 📝 Ajoute une mémoire
     */
    add(data, importance = 1, category = 'general') {
        try {
            const memory = {
                id: Date.now() + Math.random(),
                data,
                importance,
                category,
                timestamp: Date.now(),
                accessCount: 0
            };
            
            this.memories.set(memory.id, memory);
            this.stats.totalMemories++;
            this.stats.totalSize += JSON.stringify(memory).length;
            
            this.emit('memoryAdded', memory);
            
            return memory;
            
        } catch (error) {
            console.error('❌ Erreur ajout mémoire:', error.message);
            return null;
        }
    }
    
    /**
     * 🔍 Recherche dans les mémoires
     */
    search(query, limit = 10) {
        try {
            const results = [];
            const lowerQuery = query.toLowerCase();
            
            for (const memory of this.memories.values()) {
                if (memory.data.toLowerCase().includes(lowerQuery)) {
                    memory.accessCount++;
                    results.push(memory);
                }
            }
            
            // Trier par importance et fréquence d'accès
            results.sort((a, b) => {
                return (b.importance * b.accessCount) - (a.importance * a.accessCount);
            });
            
            return results.slice(0, limit);
            
        } catch (error) {
            console.error('❌ Erreur recherche mémoire:', error.message);
            return [];
        }
    }
    
    /**
     * 📊 Obtient les statistiques
     */
    getStats() {
        return {
            ...this.stats,
            memoriesCount: this.memories.size,
            categories: this.getCategoriesStats()
        };
    }
    
    /**
     * 📈 Statistiques par catégorie
     */
    getCategoriesStats() {
        const categories = {};
        
        for (const memory of this.memories.values()) {
            if (!categories[memory.category]) {
                categories[memory.category] = 0;
            }
            categories[memory.category]++;
        }
        
        return categories;
    }
    
    /**
     * 🧹 Nettoie les anciennes mémoires
     */
    cleanup() {
        try {
            if (this.memories.size > this.config.maxMemories) {
                const memoriesArray = Array.from(this.memories.values());
                
                // Trier par importance et date
                memoriesArray.sort((a, b) => {
                    return (a.importance * a.accessCount) - (b.importance * b.accessCount);
                });
                
                // Supprimer les moins importantes
                const toRemove = memoriesArray.slice(0, this.memories.size - this.config.maxMemories);
                
                for (const memory of toRemove) {
                    this.memories.delete(memory.id);
                }
                
                console.log(`🧹 ${toRemove.length} mémoires nettoyées`);
            }
            
        } catch (error) {
            console.error('❌ Erreur nettoyage mémoire:', error.message);
        }
    }
    
    /**
     * 💾 Sauvegarde les mémoires
     */
    save() {
        try {
            const dataFile = path.join(this.config.dataPath, 'memories.json');
            const data = {
                memories: Array.from(this.memories.entries()),
                stats: this.stats,
                timestamp: Date.now()
            };
            
            fs.writeFileSync(dataFile, JSON.stringify(data, null, 2));
            console.log('💾 Mémoires sauvegardées');
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde mémoire:', error.message);
        }
    }
    
    /**
     * 📂 Charge les mémoires
     */
    load() {
        try {
            const dataFile = path.join(this.config.dataPath, 'memories.json');
            
            if (fs.existsSync(dataFile)) {
                const data = JSON.parse(fs.readFileSync(dataFile, 'utf8'));
                
                this.memories = new Map(data.memories);
                this.stats = data.stats || this.stats;
                
                console.log(`📂 ${this.memories.size} mémoires chargées`);
            }
            
        } catch (error) {
            console.error('❌ Erreur chargement mémoire:', error.message);
        }
    }
}

module.exports = ThermalMemory;
