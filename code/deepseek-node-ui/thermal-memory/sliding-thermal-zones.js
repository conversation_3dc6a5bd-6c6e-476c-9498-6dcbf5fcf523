const EventEmitter = require('events');

/**
 * 🌡️ ZONES THERMIQUES GLISSANTES
 * Système de zones thermiques dynamiques
 */
class SlidingThermalZones extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            maxZones: config.maxZones || 20,
            baseTemperature: config.baseTemperature || 37.0,
            maxTemperature: config.maxTemperature || 42.0,
            minTemperature: config.minTemperature || 35.0,
            slidingSpeed: config.slidingSpeed || 0.1
        };
        
        this.zones = new Map();
        this.activeZones = new Set();
        this.temperatureHistory = [];
        
        this.stats = {
            totalZones: 0,
            activeZones: 0,
            averageTemperature: this.config.baseTemperature,
            maxRecordedTemp: this.config.baseTemperature,
            minRecordedTemp: this.config.baseTemperature
        };
        
        console.log('🌡️ Zones Thermiques Glissantes initialisées');
        this.initialize();
    }
    
    /**
     * 🚀 Initialise les zones thermiques
     */
    initialize() {
        try {
            // Créer les zones de base
            this.createBaseZones();
            
            // Démarrer le système de glissement
            this.startSlidingSystem();
            
            // Démarrer le monitoring
            this.startTemperatureMonitoring();
            
            this.emit('initialized');
            
        } catch (error) {
            console.error('❌ Erreur initialisation zones thermiques:', error.message);
            this.emit('error', error);
        }
    }
    
    /**
     * 🏗️ Crée les zones de base
     */
    createBaseZones() {
        const baseZones = [
            { name: 'cortex-frontal', x: 0, y: 0, radius: 50, temp: 37.2 },
            { name: 'cortex-parietal', x: 100, y: 0, radius: 45, temp: 37.0 },
            { name: 'cortex-temporal', x: 0, y: 100, radius: 40, temp: 37.1 },
            { name: 'cortex-occipital', x: 100, y: 100, radius: 35, temp: 36.9 },
            { name: 'hippocampe', x: 50, y: 50, radius: 30, temp: 37.3 },
            { name: 'cervelet', x: 150, y: 50, radius: 60, temp: 36.8 },
            { name: 'thalamus', x: 75, y: 25, radius: 25, temp: 37.4 },
            { name: 'hypothalamus', x: 75, y: 75, radius: 20, temp: 37.5 }
        ];
        
        for (const zoneData of baseZones) {
            this.createZone(zoneData);
        }
        
        console.log(`🌡️ ${baseZones.length} zones thermiques de base créées`);
    }
    
    /**
     * ➕ Crée une zone thermique
     */
    createZone(zoneData) {
        const zone = {
            id: `zone_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
            name: zoneData.name,
            position: {
                x: zoneData.x,
                y: zoneData.y,
                z: zoneData.z || 0
            },
            radius: zoneData.radius,
            temperature: zoneData.temp,
            baseTemperature: zoneData.temp,
            velocity: {
                x: (Math.random() - 0.5) * this.config.slidingSpeed,
                y: (Math.random() - 0.5) * this.config.slidingSpeed,
                z: (Math.random() - 0.5) * this.config.slidingSpeed * 0.5
            },
            activity: 0,
            lastActivity: Date.now(),
            connections: [],
            created: Date.now()
        };
        
        this.zones.set(zone.id, zone);
        this.activeZones.add(zone.id);
        this.stats.totalZones++;
        this.stats.activeZones++;
        
        this.emit('zoneCreated', zone);
        
        return zone;
    }
    
    /**
     * 🔄 Démarre le système de glissement
     */
    startSlidingSystem() {
        setInterval(() => {
            this.updateZonePositions();
            this.updateZoneTemperatures();
            this.handleZoneInteractions();
        }, 100); // Mise à jour toutes les 100ms
        
        console.log('🔄 Système de glissement des zones démarré');
    }
    
    /**
     * 📍 Met à jour les positions des zones
     */
    updateZonePositions() {
        for (const zone of this.zones.values()) {
            if (!this.activeZones.has(zone.id)) continue;
            
            // Appliquer la vélocité
            zone.position.x += zone.velocity.x;
            zone.position.y += zone.velocity.y;
            zone.position.z += zone.velocity.z;
            
            // Gérer les collisions avec les bords
            this.handleBoundaryCollisions(zone);
            
            // Ajouter un peu de bruit pour le mouvement naturel
            zone.velocity.x += (Math.random() - 0.5) * 0.01;
            zone.velocity.y += (Math.random() - 0.5) * 0.01;
            zone.velocity.z += (Math.random() - 0.5) * 0.005;
            
            // Limiter la vitesse
            const maxSpeed = this.config.slidingSpeed * 2;
            zone.velocity.x = Math.max(-maxSpeed, Math.min(maxSpeed, zone.velocity.x));
            zone.velocity.y = Math.max(-maxSpeed, Math.min(maxSpeed, zone.velocity.y));
            zone.velocity.z = Math.max(-maxSpeed, Math.min(maxSpeed, zone.velocity.z));
        }
    }
    
    /**
     * 🚧 Gère les collisions avec les bords
     */
    handleBoundaryCollisions(zone) {
        const bounds = { x: 200, y: 200, z: 100 };
        
        if (zone.position.x <= 0 || zone.position.x >= bounds.x) {
            zone.velocity.x *= -0.8; // Rebond avec amortissement
            zone.position.x = Math.max(0, Math.min(bounds.x, zone.position.x));
        }
        
        if (zone.position.y <= 0 || zone.position.y >= bounds.y) {
            zone.velocity.y *= -0.8;
            zone.position.y = Math.max(0, Math.min(bounds.y, zone.position.y));
        }
        
        if (zone.position.z <= 0 || zone.position.z >= bounds.z) {
            zone.velocity.z *= -0.8;
            zone.position.z = Math.max(0, Math.min(bounds.z, zone.position.z));
        }
    }
    
    /**
     * 🌡️ Met à jour les températures des zones
     */
    updateZoneTemperatures() {
        for (const zone of this.zones.values()) {
            if (!this.activeZones.has(zone.id)) continue;
            
            // Facteur d'activité
            const activityFactor = Math.min(2.0, zone.activity / 10);
            
            // Température basée sur l'activité
            const targetTemp = zone.baseTemperature + activityFactor * 0.5;
            
            // Transition graduelle vers la température cible
            const tempDiff = targetTemp - zone.temperature;
            zone.temperature += tempDiff * 0.1;
            
            // Limiter la température
            zone.temperature = Math.max(
                this.config.minTemperature,
                Math.min(this.config.maxTemperature, zone.temperature)
            );
            
            // Diminuer l'activité au fil du temps
            zone.activity *= 0.95;
            
            // Mettre à jour les statistiques
            this.updateTemperatureStats(zone.temperature);
        }
    }
    
    /**
     * 🔗 Gère les interactions entre zones
     */
    handleZoneInteractions() {
        const zones = Array.from(this.zones.values()).filter(z => this.activeZones.has(z.id));
        
        for (let i = 0; i < zones.length; i++) {
            for (let j = i + 1; j < zones.length; j++) {
                const zone1 = zones[i];
                const zone2 = zones[j];
                
                const distance = this.calculateDistance(zone1.position, zone2.position);
                const interactionRadius = zone1.radius + zone2.radius;
                
                if (distance < interactionRadius) {
                    this.handleZoneInteraction(zone1, zone2, distance);
                }
            }
        }
    }
    
    /**
     * 📏 Calcule la distance entre deux points
     */
    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    /**
     * 🤝 Gère l'interaction entre deux zones
     */
    handleZoneInteraction(zone1, zone2, distance) {
        // Échange de chaleur
        const tempDiff = zone1.temperature - zone2.temperature;
        const heatExchange = tempDiff * 0.01;
        
        zone1.temperature -= heatExchange;
        zone2.temperature += heatExchange;
        
        // Répulsion pour éviter la superposition
        const repulsionForce = 0.1 / (distance + 0.1);
        
        const dx = zone1.position.x - zone2.position.x;
        const dy = zone1.position.y - zone2.position.y;
        const dz = zone1.position.z - zone2.position.z;
        
        zone1.velocity.x += dx * repulsionForce;
        zone1.velocity.y += dy * repulsionForce;
        zone1.velocity.z += dz * repulsionForce;
        
        zone2.velocity.x -= dx * repulsionForce;
        zone2.velocity.y -= dy * repulsionForce;
        zone2.velocity.z -= dz * repulsionForce;
        
        // Augmenter l'activité
        zone1.activity += 0.5;
        zone2.activity += 0.5;
        
        this.emit('zoneInteraction', { zone1: zone1.id, zone2: zone2.id, distance });
    }
    
    /**
     * 🔥 Active une zone (augmente son activité)
     */
    activateZone(zoneName, intensity = 1.0) {
        for (const zone of this.zones.values()) {
            if (zone.name === zoneName) {
                zone.activity += intensity;
                zone.lastActivity = Date.now();
                
                this.emit('zoneActivated', { zone: zone.id, intensity });
                
                return zone;
            }
        }
        
        return null;
    }
    
    /**
     * 📊 Démarre le monitoring de température
     */
    startTemperatureMonitoring() {
        setInterval(() => {
            this.recordTemperatureSnapshot();
        }, 5000); // Enregistrement toutes les 5 secondes
        
        console.log('📊 Monitoring de température démarré');
    }
    
    /**
     * 📸 Enregistre un instantané de température
     */
    recordTemperatureSnapshot() {
        const snapshot = {
            timestamp: Date.now(),
            zones: Array.from(this.zones.values()).map(zone => ({
                id: zone.id,
                name: zone.name,
                temperature: zone.temperature,
                activity: zone.activity,
                position: { ...zone.position }
            })),
            averageTemp: this.stats.averageTemperature
        };
        
        this.temperatureHistory.push(snapshot);
        
        // Garder seulement les 100 derniers instantanés
        if (this.temperatureHistory.length > 100) {
            this.temperatureHistory.shift();
        }
        
        this.emit('temperatureSnapshot', snapshot);
    }
    
    /**
     * 📊 Met à jour les statistiques de température
     */
    updateTemperatureStats(temperature) {
        this.stats.maxRecordedTemp = Math.max(this.stats.maxRecordedTemp, temperature);
        this.stats.minRecordedTemp = Math.min(this.stats.minRecordedTemp, temperature);
        
        // Calculer la température moyenne
        const allTemps = Array.from(this.zones.values()).map(z => z.temperature);
        this.stats.averageTemperature = allTemps.reduce((a, b) => a + b, 0) / allTemps.length;
    }
    
    /**
     * 📊 Obtient les statistiques
     */
    getStats() {
        return {
            ...this.stats,
            zones: Array.from(this.zones.values()),
            temperatureHistory: this.temperatureHistory.slice(-10) // 10 derniers
        };
    }
}

module.exports = SlidingThermalZones;
