const EventEmitter = require('events');
const os = require('os');

/**
 * 🌡️ RÉGULATION DE TEMPÉRATURE
 * Système de régulation thermique pour la mémoire
 */
class TemperatureRegulation extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            targetTemperature: config.targetTemperature || 37.0,
            maxTemperature: config.maxTemperature || 42.0,
            minTemperature: config.minTemperature || 35.0,
            regulationSpeed: config.regulationSpeed || 0.1,
            cpuInfluence: config.cpuInfluence || 0.3,
            memoryInfluence: config.memoryInfluence || 0.2
        };
        
        this.currentTemperature = this.config.targetTemperature;
        this.targetTemperature = this.config.targetTemperature;
        this.regulationActive = false;
        
        this.stats = {
            regulationCycles: 0,
            averageTemperature: this.config.targetTemperature,
            maxRecordedTemp: this.config.targetTemperature,
            minRecordedTemp: this.config.targetTemperature,
            cpuTemperature: 0,
            memoryUsage: 0
        };
        
        this.temperatureHistory = [];
        this.regulationHistory = [];
        
        console.log('🌡️ Régulation de Température initialisée');
        this.initialize();
    }
    
    /**
     * 🚀 Initialise la régulation
     */
    initialize() {
        try {
            // Démarrer le monitoring système
            this.startSystemMonitoring();
            
            // Démarrer la régulation
            this.startRegulation();
            
            // Démarrer l'enregistrement
            this.startLogging();
            
            this.emit('initialized');
            
        } catch (error) {
            console.error('❌ Erreur initialisation régulation température:', error.message);
            this.emit('error', error);
        }
    }
    
    /**
     * 📊 Démarre le monitoring système
     */
    startSystemMonitoring() {
        setInterval(() => {
            this.updateSystemMetrics();
        }, 2000); // Mise à jour toutes les 2 secondes
        
        console.log('📊 Monitoring système démarré');
    }
    
    /**
     * 📈 Met à jour les métriques système
     */
    updateSystemMetrics() {
        try {
            // Température CPU (simulation basée sur la charge)
            const cpuUsage = this.getCPUUsage();
            this.stats.cpuTemperature = 30 + (cpuUsage * 0.5); // 30-80°C
            
            // Utilisation mémoire
            const memInfo = process.memoryUsage();
            this.stats.memoryUsage = (memInfo.heapUsed / memInfo.heapTotal) * 100;
            
            // Influence sur la température cible
            this.updateTargetTemperature();
            
        } catch (error) {
            console.error('❌ Erreur mise à jour métriques:', error.message);
        }
    }
    
    /**
     * 💻 Obtient l'utilisation CPU
     */
    getCPUUsage() {
        const cpus = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;
        
        for (const cpu of cpus) {
            for (const type in cpu.times) {
                totalTick += cpu.times[type];
            }
            totalIdle += cpu.times.idle;
        }
        
        const idle = totalIdle / cpus.length;
        const total = totalTick / cpus.length;
        
        return 100 - ~~(100 * idle / total);
    }
    
    /**
     * 🎯 Met à jour la température cible
     */
    updateTargetTemperature() {
        let newTarget = this.config.targetTemperature;
        
        // Influence du CPU
        const cpuFactor = (this.stats.cpuTemperature - 50) / 100; // Normaliser
        newTarget += cpuFactor * this.config.cpuInfluence;
        
        // Influence de la mémoire
        const memoryFactor = (this.stats.memoryUsage - 50) / 100; // Normaliser
        newTarget += memoryFactor * this.config.memoryInfluence;
        
        // Limiter la température cible
        this.targetTemperature = Math.max(
            this.config.minTemperature,
            Math.min(this.config.maxTemperature, newTarget)
        );
    }
    
    /**
     * 🔄 Démarre la régulation
     */
    startRegulation() {
        this.regulationActive = true;
        
        setInterval(() => {
            if (this.regulationActive) {
                this.performRegulationCycle();
            }
        }, 500); // Régulation toutes les 500ms
        
        console.log('🔄 Régulation de température démarrée');
    }
    
    /**
     * ⚙️ Effectue un cycle de régulation
     */
    performRegulationCycle() {
        try {
            const tempDifference = this.targetTemperature - this.currentTemperature;
            
            // Calcul de l'ajustement
            let adjustment = tempDifference * this.config.regulationSpeed;
            
            // Appliquer des facteurs de régulation
            adjustment = this.applyRegulationFactors(adjustment, tempDifference);
            
            // Appliquer l'ajustement
            this.currentTemperature += adjustment;
            
            // Limiter la température
            this.currentTemperature = Math.max(
                this.config.minTemperature,
                Math.min(this.config.maxTemperature, this.currentTemperature)
            );
            
            // Enregistrer le cycle
            this.recordRegulationCycle(tempDifference, adjustment);
            
            // Mettre à jour les statistiques
            this.updateStats();
            
            // Émettre l'événement
            this.emit('temperatureRegulated', {
                current: this.currentTemperature,
                target: this.targetTemperature,
                adjustment
            });
            
        } catch (error) {
            console.error('❌ Erreur cycle régulation:', error.message);
        }
    }
    
    /**
     * 🔧 Applique les facteurs de régulation
     */
    applyRegulationFactors(adjustment, tempDifference) {
        // Facteur PID simplifié
        const proportional = tempDifference;
        const derivative = this.getTemperatureDerivative();
        
        // Ajustement proportionnel
        let finalAdjustment = proportional * 0.1;
        
        // Ajustement dérivé (pour éviter les oscillations)
        finalAdjustment += derivative * 0.05;
        
        // Limitation de l'ajustement
        const maxAdjustment = 0.5;
        return Math.max(-maxAdjustment, Math.min(maxAdjustment, finalAdjustment));
    }
    
    /**
     * 📈 Obtient la dérivée de température
     */
    getTemperatureDerivative() {
        if (this.temperatureHistory.length < 2) return 0;
        
        const recent = this.temperatureHistory.slice(-2);
        return recent[1].temperature - recent[0].temperature;
    }
    
    /**
     * 📝 Enregistre un cycle de régulation
     */
    recordRegulationCycle(tempDifference, adjustment) {
        const cycle = {
            timestamp: Date.now(),
            currentTemp: this.currentTemperature,
            targetTemp: this.targetTemperature,
            difference: tempDifference,
            adjustment,
            cpuTemp: this.stats.cpuTemperature,
            memoryUsage: this.stats.memoryUsage
        };
        
        this.regulationHistory.push(cycle);
        
        // Garder seulement les 50 derniers cycles
        if (this.regulationHistory.length > 50) {
            this.regulationHistory.shift();
        }
        
        this.stats.regulationCycles++;
    }
    
    /**
     * 📊 Met à jour les statistiques
     */
    updateStats() {
        this.stats.maxRecordedTemp = Math.max(this.stats.maxRecordedTemp, this.currentTemperature);
        this.stats.minRecordedTemp = Math.min(this.stats.minRecordedTemp, this.currentTemperature);
        
        // Calculer la température moyenne
        if (this.temperatureHistory.length > 0) {
            const sum = this.temperatureHistory.reduce((acc, record) => acc + record.temperature, 0);
            this.stats.averageTemperature = sum / this.temperatureHistory.length;
        }
    }
    
    /**
     * 📋 Démarre l'enregistrement
     */
    startLogging() {
        setInterval(() => {
            this.logTemperature();
        }, 5000); // Enregistrement toutes les 5 secondes
        
        console.log('📋 Enregistrement température démarré');
    }
    
    /**
     * 📝 Enregistre la température
     */
    logTemperature() {
        const record = {
            timestamp: Date.now(),
            temperature: this.currentTemperature,
            target: this.targetTemperature,
            cpuTemp: this.stats.cpuTemperature,
            memoryUsage: this.stats.memoryUsage
        };
        
        this.temperatureHistory.push(record);
        
        // Garder seulement les 100 derniers enregistrements
        if (this.temperatureHistory.length > 100) {
            this.temperatureHistory.shift();
        }
        
        this.emit('temperatureLogged', record);
    }
    
    /**
     * 🎯 Définit une nouvelle température cible
     */
    setTargetTemperature(temperature) {
        if (temperature >= this.config.minTemperature && temperature <= this.config.maxTemperature) {
            this.targetTemperature = temperature;
            
            this.emit('targetTemperatureChanged', {
                oldTarget: this.targetTemperature,
                newTarget: temperature
            });
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 🔥 Applique un boost de température
     */
    applyTemperatureBoost(boost, duration = 5000) {
        const originalTarget = this.targetTemperature;
        const boostedTarget = Math.min(this.config.maxTemperature, this.targetTemperature + boost);
        
        this.setTargetTemperature(boostedTarget);
        
        setTimeout(() => {
            this.setTargetTemperature(originalTarget);
        }, duration);
        
        this.emit('temperatureBoostApplied', { boost, duration });
    }
    
    /**
     * ❄️ Applique un refroidissement
     */
    applyCooling(cooling, duration = 5000) {
        const originalTarget = this.targetTemperature;
        const cooledTarget = Math.max(this.config.minTemperature, this.targetTemperature - cooling);
        
        this.setTargetTemperature(cooledTarget);
        
        setTimeout(() => {
            this.setTargetTemperature(originalTarget);
        }, duration);
        
        this.emit('coolingApplied', { cooling, duration });
    }
    
    /**
     * 📊 Obtient la température actuelle
     */
    getCurrentTemperature() {
        return this.currentTemperature;
    }
    
    /**
     * 📊 Obtient les statistiques
     */
    getStats() {
        return {
            ...this.stats,
            currentTemperature: this.currentTemperature,
            targetTemperature: this.targetTemperature,
            regulationActive: this.regulationActive,
            temperatureHistory: this.temperatureHistory.slice(-10),
            regulationHistory: this.regulationHistory.slice(-10)
        };
    }
    
    /**
     * 🛑 Arrête la régulation
     */
    stop() {
        this.regulationActive = false;
        console.log('🛑 Régulation de température arrêtée');
    }
}

module.exports = TemperatureRegulation;
