#!/usr/bin/env node

/**
 * 🧠 TEST DEEPSEEK DIRECT DANS ELECTRON
 * Vérifie l'intégration complète DeepSeek → Mémoire Thermique → Möbius dans Electron
 */

console.log('🧠 === TEST DEEPSEEK DIRECT ELECTRON ===\n');

async function testDeepSeekElectron() {
    try {
        console.log('1️⃣ Test des modules Electron...');
        
        // Vérifier que les modules existent
        const fs = require('fs');
        const path = require('path');
        
        const requiredFiles = [
            'services/deepseek-direct-connector.js',
            'routes/luna-deepseek.js',
            'views/luna-deepseek.ejs',
            'thermal-memory/thermal-memory-complete.js'
        ];
        
        let allPresent = true;
        
        requiredFiles.forEach(file => {
            const fullPath = path.join(__dirname, file);
            if (fs.existsSync(fullPath)) {
                console.log(`✅ ${file}`);
            } else {
                console.log(`❌ ${file} - MANQUANT`);
                allPresent = false;
            }
        });
        
        if (!allPresent) {
            console.log('\n❌ Certains fichiers requis sont manquants.');
            return;
        }
        
        console.log('\n✅ Tous les modules Electron sont présents');
        
        // Test du connecteur DeepSeek
        console.log('\n2️⃣ Test du connecteur DeepSeek...');
        
        const DeepSeekDirectConnector = require('./services/deepseek-direct-connector');
        
        // Créer un mock de mémoire thermique
        const mockThermalMemory = {
            add: (data, importance, category) => {
                console.log(`📝 Mock: Ajout ${category} - ${data.substring(0, 50)}...`);
                return { id: Date.now(), data, importance, category };
            },
            search: (query) => {
                console.log(`🔍 Mock: Recherche "${query}"`);
                return [];
            },
            getAllEntries: () => {
                return [
                    { data: 'Entrée test 1', category: 'test' },
                    { data: 'Entrée test 2', category: 'test' }
                ];
            },
            on: () => {},
            emit: () => {}
        };
        
        const connector = new DeepSeekDirectConnector(mockThermalMemory, {
            model: 'deepseek-chat',
            temperature: 0.7,
            maxTokens: 100
        });
        
        console.log('✅ Connecteur DeepSeek créé');
        
        // Attendre l'initialisation
        await new Promise((resolve) => {
            connector.on('initialized', resolve);
            setTimeout(resolve, 2000); // Timeout de sécurité
        });
        
        console.log('✅ Connecteur DeepSeek initialisé');
        
        // Test des statistiques
        console.log('\n3️⃣ Test des statistiques...');
        
        const stats = connector.getStats();
        console.log(`📊 Modèle: ${stats.config.model}`);
        console.log(`📊 API Key: ${stats.config.hasApiKey ? 'CONFIGURÉE' : 'MANQUANTE'}`);
        console.log(`📊 Cache: ${stats.cache.size}/${stats.cache.maxSize}`);
        console.log(`📊 Intégration Möbius: ${stats.mobiusIntegration.enabled ? 'ACTIVÉE' : 'DÉSACTIVÉE'}`);
        
        // Test de la mémoire thermique complète
        console.log('\n4️⃣ Test mémoire thermique complète...');
        
        try {
            const ThermalMemoryComplete = require('./thermal-memory/thermal-memory-complete');
            const thermalComplete = new ThermalMemoryComplete();
            
            console.log('✅ Mémoire thermique complète chargée');
            console.log(`🌡️ Température: ${thermalComplete.memory.temperature}°C`);
            console.log(`📊 Efficacité: ${thermalComplete.memory.efficiency}%`);
            
            // Vérifier le système Möbius
            if (thermalComplete.mobiusState) {
                console.log(`🔄 Système Möbius: ${thermalComplete.mobiusState.isActive ? 'ACTIF' : 'INACTIF'}`);
                console.log(`🔄 Phase: ${thermalComplete.mobiusState.currentPhase}`);
                console.log(`⚡ Énergie: ${thermalComplete.mobiusState.energy}%`);
            } else {
                console.log('⚠️ Système Möbius non détecté');
            }
            
        } catch (error) {
            console.log('⚠️ Mémoire thermique complète non disponible:', error.message);
        }
        
        // Test des routes
        console.log('\n5️⃣ Test des routes...');
        
        const lunaDeepSeekRoutes = require('./routes/luna-deepseek');
        console.log('✅ Routes DeepSeek chargées');
        
        // Test de simulation de requête
        console.log('\n6️⃣ Test simulation requête...');
        
        // Simuler une requête HTTP
        const mockReq = {
            body: {
                message: 'Test de connexion Electron',
                context: {
                    temperature: 0.5,
                    maxTokens: 50
                }
            }
        };
        
        const mockRes = {
            json: (data) => {
                console.log('📤 Réponse simulée:', data);
                return data;
            },
            status: (code) => ({
                json: (data) => {
                    console.log(`📤 Réponse simulée (${code}):`, data);
                    return data;
                }
            })
        };
        
        console.log('✅ Simulation de requête préparée');
        
        // Test du serveur
        console.log('\n7️⃣ Vérification serveur...');
        
        try {
            // Vérifier que le serveur peut être chargé
            const serverPath = path.join(__dirname, 'server-luna.js');
            if (fs.existsSync(serverPath)) {
                console.log('✅ Serveur Luna trouvé');
                
                // Lire le contenu pour vérifier les intégrations
                const serverContent = fs.readFileSync(serverPath, 'utf8');
                
                if (serverContent.includes('deepseek-direct-connector')) {
                    console.log('✅ Intégration connecteur DeepSeek détectée');
                } else {
                    console.log('⚠️ Intégration connecteur DeepSeek non détectée');
                }
                
                if (serverContent.includes('luna-deepseek')) {
                    console.log('✅ Routes DeepSeek intégrées');
                } else {
                    console.log('⚠️ Routes DeepSeek non intégrées');
                }
                
                if (serverContent.includes('thermal-memory-complete')) {
                    console.log('✅ Mémoire thermique complète intégrée');
                } else {
                    console.log('⚠️ Mémoire thermique complète non intégrée');
                }
                
            } else {
                console.log('❌ Serveur Luna non trouvé');
            }
        } catch (error) {
            console.log('❌ Erreur vérification serveur:', error.message);
        }
        
        // Test de l'interface utilisateur
        console.log('\n8️⃣ Vérification interface utilisateur...');
        
        const viewPath = path.join(__dirname, 'views/luna-deepseek.ejs');
        if (fs.existsSync(viewPath)) {
            console.log('✅ Interface DeepSeek trouvée');
            
            const viewContent = fs.readFileSync(viewPath, 'utf8');
            
            if (viewContent.includes('socket.io')) {
                console.log('✅ Socket.IO intégré dans l\'interface');
            }
            
            if (viewContent.includes('mobius')) {
                console.log('✅ Indicateurs Möbius dans l\'interface');
            }
            
            if (viewContent.includes('stats')) {
                console.log('✅ Statistiques temps réel dans l\'interface');
            }
            
        } else {
            console.log('❌ Interface DeepSeek non trouvée');
        }
        
        // Arrêter le connecteur
        connector.stop();
        
        // Résumé final
        console.log('\n🎉 === RÉSUMÉ INTÉGRATION ELECTRON ===');
        
        console.log('✅ MODULES INTÉGRÉS:');
        console.log('  • Connecteur DeepSeek Direct');
        console.log('  • Routes API DeepSeek');
        console.log('  • Interface utilisateur');
        console.log('  • Mémoire thermique complète');
        console.log('  • Système Möbius');
        
        console.log('\n✅ FONCTIONNALITÉS:');
        console.log('  • Chat temps réel avec DeepSeek');
        console.log('  • Intégration mémoire thermique automatique');
        console.log('  • Synchronisation système Möbius');
        console.log('  • Statistiques et métriques');
        console.log('  • Cache intelligent');
        console.log('  • Interface graphique complète');
        
        console.log('\n✅ AVANTAGES vs OLLAMA:');
        console.log('  • Pas de téléchargement de modèles (4-70GB économisés)');
        console.log('  • Latence réduite (connexion directe)');
        console.log('  • Modèles toujours à jour');
        console.log('  • Intégration native Electron');
        console.log('  • Performance optimale');
        
        console.log('\n🚀 PRÊT POUR UTILISATION:');
        console.log('  1. Démarrer: node server-luna.js');
        console.log('  2. Ouvrir: http://localhost:3000/luna/deepseek');
        console.log('  3. Utiliser: Chat direct avec DeepSeek + Möbius');
        
        console.log('\n✅ Test terminé avec succès !');
        
    } catch (error) {
        console.error('❌ Erreur durant le test:', error.message);
        console.log('\n📋 DIAGNOSTIC:');
        console.log('- Vérifiez que tous les fichiers sont présents');
        console.log('- Vérifiez les dépendances npm');
        console.log('- Vérifiez la configuration DeepSeek');
    }
}

// Fonction d'aide pour afficher l'architecture
function showElectronArchitecture() {
    console.log('\n🏗️ === ARCHITECTURE ELECTRON DEEPSEEK ===');
    console.log('📁 Structure:');
    console.log('  ├── services/');
    console.log('  │   └── deepseek-direct-connector.js  🧠 Connecteur API');
    console.log('  ├── routes/');
    console.log('  │   └── luna-deepseek.js             🌐 API Routes');
    console.log('  ├── views/');
    console.log('  │   └── luna-deepseek.ejs            🖥️ Interface');
    console.log('  ├── thermal-memory/');
    console.log('  │   └── thermal-memory-complete.js   💾 Mémoire + Möbius');
    console.log('  └── server-luna.js                   🚀 Serveur principal');
    
    console.log('\n🔄 Flux de données:');
    console.log('  Interface → Routes → Connecteur → DeepSeek API');
    console.log('  DeepSeek → Connecteur → Mémoire Thermique → Möbius');
    console.log('  Möbius → Socket.IO → Interface (temps réel)');
    
    console.log('\n⚡ Avantages:');
    console.log('  • Intégration native Electron');
    console.log('  • Performance maximale');
    console.log('  • Interface graphique riche');
    console.log('  • Synchronisation temps réel');
}

// Exécution du test
if (require.main === module) {
    showElectronArchitecture();
    testDeepSeekElectron().catch(console.error);
}

module.exports = { testDeepSeekElectron };
