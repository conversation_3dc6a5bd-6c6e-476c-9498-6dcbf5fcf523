/**
 * 🔧 CORRECTIF MÉMOIRE THERMIQUE
 * Correctifs pour la mémoire thermique
 */

const fs = require('fs');
const path = require('path');

/**
 * 🔧 Applique les correctifs pour la mémoire thermique
 */
function applyThermalMemoryFixes() {
    try {
        console.log('🔧 Application des correctifs mémoire thermique...');
        
        // Correctif 1: Vérifier les répertoires
        ensureDirectories();
        
        // Correctif 2: Initialiser les fichiers de base
        initializeBaseFiles();
        
        // Correctif 3: Corriger les permissions
        fixPermissions();
        
        console.log('✅ Correctifs mémoire thermique appliqués');
        
        return true;
        
    } catch (error) {
        console.error('❌ Erreur application correctifs:', error.message);
        return false;
    }
}

/**
 * 📁 Assure que les répertoires existent
 */
function ensureDirectories() {
    const baseDir = path.join(__dirname, '../../../MEMOIRE-REELLE');
    const dirs = [
        'neurones',
        'synapses',
        'zones-thermiques',
        'tiroirs'
    ];
    
    if (!fs.existsSync(baseDir)) {
        fs.mkdirSync(baseDir, { recursive: true });
    }
    
    for (const dir of dirs) {
        const dirPath = path.join(baseDir, dir);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
}

/**
 * 📄 Initialise les fichiers de base
 */
function initializeBaseFiles() {
    const baseDir = path.join(__dirname, '../../../MEMOIRE-REELLE');
    
    // Fichier compteurs
    const compteursPath = path.join(baseDir, 'compteurs.json');
    if (!fs.existsSync(compteursPath)) {
        const compteurs = {
            neurones_total: 86000007061,
            synapses_total: 602000000000000,
            tiroirs_total: 1,
            souvenirs_total: 0,
            derniere_mise_a_jour: Date.now()
        };
        
        fs.writeFileSync(compteursPath, JSON.stringify(compteurs, null, 2));
    }
    
    // Fichier de configuration
    const configPath = path.join(baseDir, 'config.json');
    if (!fs.existsSync(configPath)) {
        const config = {
            version: '2.0.0',
            type: 'thermal-memory-real',
            initialized: Date.now(),
            features: {
                neurones: true,
                synapses: true,
                zones_thermiques: true,
                compression: true
            }
        };
        
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    }
}

/**
 * 🔐 Corrige les permissions
 */
function fixPermissions() {
    try {
        const baseDir = path.join(__dirname, '../../../MEMOIRE-REELLE');
        
        if (fs.existsSync(baseDir)) {
            // Permissions lecture/écriture pour le propriétaire
            fs.chmodSync(baseDir, 0o755);
        }
        
    } catch (error) {
        // Ignorer les erreurs de permissions sur certains systèmes
        console.log('⚠️ Permissions non modifiées (normal sur certains systèmes)');
    }
}

module.exports = {
    applyThermalMemoryFixes,
    applyThermalMemoryFix: applyThermalMemoryFixes // Alias pour compatibilité
};
