/**
 * 📝 LOGGER SIMPLE POUR LOUNA AI
 */

class Logger {
    constructor() {
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        this.currentLevel = 2; // info par défaut
    }

    error(message, meta = {}) {
        if (this.currentLevel >= this.levels.error) {
            console.error(`❌ [ERROR] ${message}`, meta);
        }
    }

    warn(message, meta = {}) {
        if (this.currentLevel >= this.levels.warn) {
            console.warn(`⚠️ [WARN] ${message}`, meta);
        }
    }

    info(message, meta = {}) {
        if (this.currentLevel >= this.levels.info) {
            console.log(`ℹ️ [INFO] ${message}`, meta);
        }
    }

    debug(message, meta = {}) {
        if (this.currentLevel >= this.levels.debug) {
            console.log(`🔍 [DEBUG] ${message}`, meta);
        }
    }
}

const logger = new Logger();

function getLogger() {
    return logger;
}

module.exports = { getLogger, Logger };
