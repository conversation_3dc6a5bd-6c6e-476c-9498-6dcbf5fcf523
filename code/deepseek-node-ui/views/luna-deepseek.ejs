<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 DeepSeek Direct - Louna AI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .deepseek-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            margin: 20px 0;
        }
        
        .chat-container {
            height: 500px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
        }
        
        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-left: auto;
            text-align: right;
        }
        
        .message.assistant {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            margin-right: auto;
        }
        
        .message.system {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            margin: 0 auto;
            text-align: center;
            max-width: 60%;
        }
        
        .stats-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .metric {
            text-align: center;
            padding: 10px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00f2fe;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .btn-deepseek {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-deepseek:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-processing { background-color: #ffc107; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .mobius-indicator {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradient 3s ease infinite;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="deepseek-container">
                    <div class="text-center mb-4">
                        <h1><i class="fas fa-brain"></i> DeepSeek Direct</h1>
                        <p class="lead">Connexion directe à DeepSeek R1 avec intégration mémoire thermique et système Möbius</p>
                        <div id="connectionStatus">
                            <span class="status-indicator status-offline"></span>
                            <span>Vérification de la connexion...</span>
                        </div>
                    </div>
                    
                    <!-- Statistiques en temps réel -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="metric">
                                    <div class="metric-value" id="totalRequests">0</div>
                                    <div class="metric-label">Requêtes totales</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="metric">
                                    <div class="metric-value" id="successRate">0%</div>
                                    <div class="metric-label">Taux de succès</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="metric">
                                    <div class="metric-value" id="avgResponseTime">0ms</div>
                                    <div class="metric-label">Temps réponse</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="metric">
                                    <div class="metric-value" id="totalTokens">0</div>
                                    <div class="metric-label">Tokens utilisés</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Indicateur Möbius -->
                    <div class="mobius-indicator" id="mobiusIndicator" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <strong><i class="fas fa-infinity"></i> Système Möbius Actif</strong>
                                <div>Cycles: <span id="mobiusCycles">0</span></div>
                                <div>Énergie: <span id="mobiusEnergy">100%</span></div>
                            </div>
                            <div class="col-md-6">
                                <div>Phase: <span id="mobiusPhase">-</span></div>
                                <div>Efficacité: <span id="mobiusEfficiency">0%</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Zone de chat -->
                    <div class="chat-container" id="chatContainer">
                        <div class="message system">
                            <i class="fas fa-robot"></i> Interface DeepSeek Direct initialisée
                        </div>
                    </div>
                    
                    <!-- Interface de saisie -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text" class="form-control" id="messageInput" 
                                       placeholder="Posez votre question à DeepSeek..." 
                                       style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                                <button class="btn btn-deepseek" id="sendButton">
                                    <i class="fas fa-paper-plane"></i> Envoyer
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="row">
                                <div class="col-6">
                                    <button class="btn btn-outline-light btn-sm w-100" id="testButton">
                                        <i class="fas fa-vial"></i> Test
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-outline-light btn-sm w-100" id="clearButton">
                                        <i class="fas fa-trash"></i> Effacer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Configuration -->
                    <div class="mt-4">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Modèle</label>
                                <select class="form-select" id="modelSelect" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                                    <option value="deepseek-chat">deepseek-chat</option>
                                    <option value="deepseek-coder">deepseek-coder</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Température</label>
                                <input type="range" class="form-range" id="temperatureRange" min="0" max="1" step="0.1" value="0.7">
                                <div class="text-center"><span id="temperatureValue">0.7</span></div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Max Tokens</label>
                                <input type="number" class="form-control" id="maxTokensInput" value="2048" min="100" max="4000"
                                       style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/socket.io/socket.io.js"></script>
    <script>
        // Initialisation Socket.IO
        const socket = io();
        
        // Variables globales
        let isConnected = false;
        let isProcessing = false;
        
        // Éléments DOM
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const testButton = document.getElementById('testButton');
        const clearButton = document.getElementById('clearButton');
        const connectionStatus = document.getElementById('connectionStatus');
        const temperatureRange = document.getElementById('temperatureRange');
        const temperatureValue = document.getElementById('temperatureValue');
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
            loadStats();
            checkMobiusStatus();
            
            // Événements
            sendButton.addEventListener('click', sendMessage);
            testButton.addEventListener('click', testConnection);
            clearButton.addEventListener('click', clearChat);
            
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            temperatureRange.addEventListener('input', function() {
                temperatureValue.textContent = this.value;
            });
            
            // Actualiser les stats toutes les 5 secondes
            setInterval(loadStats, 5000);
            setInterval(checkMobiusStatus, 3000);
        });
        
        // Vérifier la connexion
        async function checkConnection() {
            try {
                const response = await fetch('/luna/deepseek/test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (data.success && data.test === 'passed') {
                    updateConnectionStatus(true, 'Connecté à DeepSeek');
                    addMessage('system', `✅ Connexion DeepSeek établie (${data.responseTime}ms)`);
                } else {
                    updateConnectionStatus(false, 'Erreur de connexion');
                    addMessage('system', `❌ Erreur: ${data.error || 'Connexion échouée'}`);
                }
            } catch (error) {
                updateConnectionStatus(false, 'Service indisponible');
                addMessage('system', `❌ Service DeepSeek indisponible: ${error.message}`);
            }
        }
        
        // Mettre à jour le statut de connexion
        function updateConnectionStatus(connected, message) {
            isConnected = connected;
            const indicator = connectionStatus.querySelector('.status-indicator');
            const text = connectionStatus.querySelector('span:last-child');
            
            indicator.className = `status-indicator ${connected ? 'status-online' : 'status-offline'}`;
            text.textContent = message;
        }
        
        // Envoyer un message
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isProcessing || !isConnected) return;
            
            isProcessing = true;
            updateConnectionStatus(true, 'Traitement en cours...');
            connectionStatus.querySelector('.status-indicator').className = 'status-indicator status-processing';
            
            addMessage('user', message);
            messageInput.value = '';
            
            try {
                const response = await fetch('/luna/deepseek/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message,
                        context: {
                            temperature: parseFloat(temperatureRange.value),
                            maxTokens: parseInt(document.getElementById('maxTokensInput').value),
                            model: document.getElementById('modelSelect').value
                        }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addMessage('assistant', data.response);
                    addMessage('system', `📊 ${data.model} • ${data.tokensUsed} tokens • ${data.responseTime}ms`);
                } else {
                    addMessage('system', `❌ Erreur: ${data.error}`);
                }
                
            } catch (error) {
                addMessage('system', `❌ Erreur réseau: ${error.message}`);
            } finally {
                isProcessing = false;
                updateConnectionStatus(true, 'Connecté à DeepSeek');
                loadStats();
            }
        }
        
        // Ajouter un message au chat
        function addMessage(type, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            if (type === 'user') {
                messageDiv.innerHTML = `<i class="fas fa-user"></i> ${content}`;
            } else if (type === 'assistant') {
                messageDiv.innerHTML = `<i class="fas fa-robot"></i> ${content}`;
            } else {
                messageDiv.innerHTML = content;
            }
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // Charger les statistiques
        async function loadStats() {
            try {
                const response = await fetch('/luna/deepseek/stats');
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.stats;
                    document.getElementById('totalRequests').textContent = stats.metrics.totalRequests;
                    
                    const successRate = stats.metrics.totalRequests > 0 
                        ? ((stats.metrics.successfulRequests / stats.metrics.totalRequests) * 100).toFixed(1)
                        : 0;
                    document.getElementById('successRate').textContent = successRate + '%';
                    
                    document.getElementById('avgResponseTime').textContent = Math.round(stats.metrics.averageResponseTime) + 'ms';
                    document.getElementById('totalTokens').textContent = stats.metrics.totalTokensUsed;
                }
            } catch (error) {
                console.error('Erreur chargement stats:', error);
            }
        }
        
        // Vérifier le statut Möbius
        async function checkMobiusStatus() {
            try {
                const response = await fetch('/luna/deepseek/mobius');
                const data = await response.json();
                
                const mobiusIndicator = document.getElementById('mobiusIndicator');
                
                if (data.success && data.available && data.mobius) {
                    mobiusIndicator.style.display = 'block';
                    
                    if (data.mobius.state) {
                        document.getElementById('mobiusCycles').textContent = data.mobius.state.cycleCount || 0;
                        document.getElementById('mobiusEnergy').textContent = (data.mobius.state.energy || 100).toFixed(1) + '%';
                        document.getElementById('mobiusPhase').textContent = data.mobius.state.currentPhase || '-';
                        document.getElementById('mobiusEfficiency').textContent = ((data.mobius.state.efficiency || 0) * 100).toFixed(1) + '%';
                    }
                } else {
                    mobiusIndicator.style.display = 'none';
                }
            } catch (error) {
                console.error('Erreur vérification Möbius:', error);
            }
        }
        
        // Tester la connexion
        function testConnection() {
            checkConnection();
        }
        
        // Effacer le chat
        function clearChat() {
            chatContainer.innerHTML = '<div class="message system"><i class="fas fa-robot"></i> Chat effacé</div>';
        }
        
        // Événements Socket.IO
        socket.on('deepseek response', function(data) {
            console.log('Réponse DeepSeek reçue:', data);
        });
        
        socket.on('deepseek error', function(data) {
            console.error('Erreur DeepSeek:', data);
        });
        
        socket.on('mobiusCycleCompleted', function(data) {
            console.log('Cycle Möbius complété:', data);
            checkMobiusStatus();
        });
    </script>
</body>
</html>
