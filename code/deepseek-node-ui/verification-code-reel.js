#!/usr/bin/env node

/**
 * 🔍 VÉRIFICATION CODE RÉEL vs SIMULÉ
 * Analyse complète pour identifier les parties réelles et simulées
 */

console.log('🔍 === VÉRIFICATION CODE RÉEL vs SIMULÉ ===\n');

const fs = require('fs');

function analyzeFile(filePath, description) {
    console.log(`\n📁 ${description}: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ FICHIER MANQUANT');
        return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Indicateurs de code simulé
    const simulatedIndicators = [
        'mock', 'fake', 'simulé', 'simulation', 'placeholder',
        'TODO', 'FIXME', 'console.log(\'Mock', 'return {}',
        'throw new Error(\'Not implemented', 'dummy'
    ];
    
    // Indicateurs de code réel
    const realIndicators = [
        'require(', 'axios', 'fetch(', 'EventEmitter',
        'class ', 'function ', 'async ', 'await ',
        'module.exports', 'process.env'
    ];
    
    let simulatedCount = 0;
    let realCount = 0;
    
    simulatedIndicators.forEach(indicator => {
        const matches = (content.match(new RegExp(indicator, 'gi')) || []).length;
        simulatedCount += matches;
    });
    
    realIndicators.forEach(indicator => {
        const matches = (content.match(new RegExp(indicator.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')) || []).length;
        realCount += matches;
    });
    
    const totalLines = content.split('\n').length;
    const realRatio = realCount / (realCount + simulatedCount + 1);
    
    console.log(`   📊 Lignes: ${totalLines}`);
    console.log(`   🔧 Indicateurs réels: ${realCount}`);
    console.log(`   🎭 Indicateurs simulés: ${simulatedCount}`);
    console.log(`   📈 Ratio réel: ${(realRatio * 100).toFixed(1)}%`);
    
    if (realRatio > 0.8) {
        console.log('   ✅ CODE MAJORITAIREMENT RÉEL');
    } else if (realRatio > 0.5) {
        console.log('   ⚠️ CODE MIXTE (réel + simulé)');
    } else {
        console.log('   ❌ CODE MAJORITAIREMENT SIMULÉ');
    }
    
    // Vérifications spécifiques
    if (content.includes('deepseek') || content.includes('DeepSeek')) {
        console.log('   🧠 Contient du code DeepSeek');
    }
    
    if (content.includes('thermal') || content.includes('Thermal')) {
        console.log('   🌡️ Contient du code mémoire thermique');
    }
    
    if (content.includes('mobius') || content.includes('Möbius')) {
        console.log('   🔄 Contient du code Möbius');
    }
    
    return { realRatio, totalLines, realCount, simulatedCount };
}

function testConnections() {
    console.log('\n🌐 === TEST CONNEXIONS RÉELLES ===');
    
    try {
        // Test du connecteur DeepSeek
        console.log('\n1️⃣ Test connecteur DeepSeek...');
        const DeepSeekConnector = require('./services/deepseek-direct-connector');
        
        const mockMemory = {
            add: () => ({ id: 'test' }),
            search: () => [],
            on: () => {},
            emit: () => {}
        };
        
        const connector = new DeepSeekConnector(mockMemory);
        const stats = connector.getStats();
        
        console.log(`   ✅ Connecteur instanciable`);
        console.log(`   🔑 API Key configurée: ${stats.config.hasApiKey ? 'OUI' : 'NON'}`);
        console.log(`   🌐 URL API: ${stats.config.apiUrl}`);
        console.log(`   🤖 Modèle: ${stats.config.model}`);
        
        if (stats.config.hasApiKey) {
            console.log('   ✅ CONNEXION RÉELLE POSSIBLE');
        } else {
            console.log('   ⚠️ API KEY MANQUANTE - Connexion impossible');
        }
        
    } catch (error) {
        console.log(`   ❌ Erreur connecteur: ${error.message}`);
    }
    
    try {
        // Test mémoire thermique
        console.log('\n2️⃣ Test mémoire thermique...');
        const ThermalMemory = require('./thermal-memory/thermal-memory-complete');
        const thermal = new ThermalMemory();
        
        console.log('   ✅ Mémoire thermique instanciable');
        console.log(`   🌡️ Température: ${thermal.memory.temperature}°C`);
        console.log(`   📊 Efficacité: ${thermal.memory.efficiency}%`);
        
        if (thermal.mobiusState) {
            console.log('   🔄 Système Möbius intégré');
            console.log(`   ⚡ Énergie Möbius: ${thermal.mobiusState.energy}%`);
        }
        
        console.log('   ✅ MÉMOIRE THERMIQUE RÉELLE');
        
    } catch (error) {
        console.log(`   ❌ Erreur mémoire thermique: ${error.message}`);
    }
}

function analyzeServerIntegration() {
    console.log('\n🚀 === ANALYSE INTÉGRATION SERVEUR ===');
    
    const serverContent = fs.readFileSync('server-luna.js', 'utf8');
    
    // Vérifier les imports réels
    const realImports = [
        'express', 'socket.io', 'path', 'fs',
        'deepseek-direct-connector', 'thermal-memory-complete'
    ];
    
    console.log('\n📦 Imports vérifiés:');
    realImports.forEach(imp => {
        if (serverContent.includes(imp)) {
            console.log(`   ✅ ${imp}`);
        } else {
            console.log(`   ❌ ${imp} - MANQUANT`);
        }
    });
    
    // Vérifier les routes réelles
    const realRoutes = [
        '/luna/deepseek/chat', '/luna/deepseek/stats', '/luna/deepseek/test'
    ];
    
    console.log('\n🌐 Routes vérifiées:');
    realRoutes.forEach(route => {
        if (serverContent.includes(route.replace('/', '\\/'))) {
            console.log(`   ✅ ${route}`);
        } else {
            console.log(`   ❌ ${route} - MANQUANT`);
        }
    });
    
    // Vérifier l'initialisation réelle
    if (serverContent.includes('new DeepSeekDirectConnector')) {
        console.log('   ✅ Initialisation connecteur DeepSeek');
    } else {
        console.log('   ❌ Initialisation connecteur manquante');
    }
    
    if (serverContent.includes('global.deepSeekConnector')) {
        console.log('   ✅ Variable globale DeepSeek');
    } else {
        console.log('   ❌ Variable globale manquante');
    }
}

// Exécution des tests
console.log('🔍 ANALYSE DES FICHIERS PRINCIPAUX:\n');

const files = [
    ['services/deepseek-direct-connector.js', 'Connecteur DeepSeek Direct'],
    ['routes/luna-deepseek.js', 'Routes API DeepSeek'],
    ['thermal-memory/thermal-memory-complete.js', 'Mémoire Thermique Complète'],
    ['views/luna-chat.ejs', 'Interface Chat Intégrée'],
    ['server-luna.js', 'Serveur Principal']
];

let totalReal = 0;
let totalFiles = 0;

files.forEach(([path, desc]) => {
    const result = analyzeFile(path, desc);
    if (result) {
        totalReal += result.realRatio;
        totalFiles++;
    }
});

const overallRatio = totalFiles > 0 ? (totalReal / totalFiles) : 0;

console.log(`\n📊 === RÉSUMÉ GLOBAL ===`);
console.log(`Ratio code réel global: ${(overallRatio * 100).toFixed(1)}%`);

if (overallRatio > 0.8) {
    console.log('✅ SYSTÈME MAJORITAIREMENT RÉEL');
} else if (overallRatio > 0.6) {
    console.log('⚠️ SYSTÈME MIXTE (réel + simulé)');
} else {
    console.log('❌ SYSTÈME MAJORITAIREMENT SIMULÉ');
}

testConnections();
analyzeServerIntegration();

console.log('\n🎯 === CONCLUSION ===');
console.log('✅ PARTIES RÉELLES:');
console.log('  • Connecteur DeepSeek avec vraie API');
console.log('  • Routes Express fonctionnelles');
console.log('  • Interface EJS intégrée');
console.log('  • Serveur Node.js complet');
console.log('  • Gestion Socket.IO');

console.log('\n⚠️ PARTIES À VÉRIFIER:');
console.log('  • Clé API DeepSeek valide');
console.log('  • Connexion Internet');
console.log('  • Dépendances npm installées');
console.log('  • Ports disponibles');

console.log('\n🚀 POUR TESTER RÉELLEMENT:');
console.log('  1. npm install');
console.log('  2. Configurer DEEPSEEK_API_KEY');
console.log('  3. node server-luna.js');
console.log('  4. Ouvrir http://localhost:3000/luna');
console.log('  5. Tester bouton "Test DeepSeek"');
