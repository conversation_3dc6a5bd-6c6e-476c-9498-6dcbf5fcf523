#!/usr/bin/env node

/**
 * 🧠 TEST DEEPSEEK R1 8B VIA OLLAMA
 * Test de la configuration DeepSeek R1 8B local via Ollama
 */

console.log('🧠 === TEST DEEPSEEK R1 8B VIA OLLAMA ===\n');

async function testDeepSeekR1_8B() {
    try {
        console.log('1️⃣ Vérification des modèles Ollama disponibles...');
        
        const axios = require('axios');
        
        // Vérifier Ollama
        const ollamaUrl = 'http://localhost:11434';
        
        try {
            const modelsResponse = await axios.get(`${ollamaUrl}/api/tags`, { timeout: 5000 });
            
            if (modelsResponse.data && modelsResponse.data.models) {
                const models = modelsResponse.data.models.map(m => m.name);
                console.log('✅ Modèles Ollama disponibles:');
                models.forEach(model => {
                    if (model.includes('deepseek')) {
                        console.log(`   🧠 ${model} ← DEEPSEEK TROUVÉ !`);
                    } else {
                        console.log(`   📦 ${model}`);
                    }
                });
                
                // Vérifier si deepseek-r1:8b est disponible
                const hasDeepSeek8B = models.includes('deepseek-r1:8b');
                const hasDeepSeek7B = models.includes('deepseek-r1:7b');
                
                if (hasDeepSeek8B) {
                    console.log('\n✅ DeepSeek R1 8B trouvé ! Configuration correcte.');
                } else if (hasDeepSeek7B) {
                    console.log('\n⚠️ Seulement DeepSeek R1 7B trouvé. 8B recommandé.');
                } else {
                    console.log('\n❌ Aucun modèle DeepSeek trouvé.');
                    return;
                }
                
            } else {
                console.log('❌ Réponse Ollama invalide');
                return;
            }
            
        } catch (error) {
            console.log('❌ Erreur connexion Ollama:', error.message);
            console.log('💡 Assurez-vous qu\'Ollama est démarré: ollama serve');
            return;
        }
        
        console.log('\n2️⃣ Test du connecteur DeepSeek...');
        
        // Charger le connecteur
        const DeepSeekDirectConnector = require('./services/deepseek-direct-connector');
        
        // Mock de mémoire thermique
        const mockThermalMemory = {
            add: (data, importance, category) => {
                console.log(`📝 Mémoire: ${category} - ${data.substring(0, 50)}...`);
                return { id: Date.now(), data, importance, category };
            },
            search: (query) => {
                console.log(`🔍 Recherche: "${query}"`);
                return [];
            },
            on: () => {},
            emit: () => {}
        };
        
        // Créer le connecteur avec DeepSeek R1 8B
        const connector = new DeepSeekDirectConnector(mockThermalMemory, {
            model: 'deepseek-r1:8b',
            temperature: 0.7,
            maxTokens: 100,
            ollamaUrl: 'http://localhost:11434'
        });
        
        console.log('✅ Connecteur DeepSeek R1 8B créé');
        
        // Attendre l'initialisation
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('\n3️⃣ Test de génération avec DeepSeek R1 8B...');
        
        const testPrompt = "Bonjour ! Peux-tu me dire ton nom et tes capacités en quelques mots ?";
        console.log(`📝 Prompt: ${testPrompt}`);
        
        const startTime = Date.now();
        
        const response = await connector.chat(testPrompt, {
            temperature: 0.6,
            maxTokens: 80
        });
        
        const responseTime = Date.now() - startTime;
        
        if (response.success) {
            console.log('\n✅ GÉNÉRATION RÉUSSIE !');
            console.log(`🤖 Réponse: ${response.content}`);
            console.log(`📊 Modèle: ${response.model}`);
            console.log(`🔢 Tokens: ${response.tokensUsed}`);
            console.log(`⏱️ Temps: ${responseTime}ms`);
            console.log(`🧠 Intégration thermique: ${response.thermalIntegration ? 'OUI' : 'NON'}`);
            console.log(`🔄 Intégration Möbius: ${response.mobiusIntegration ? 'OUI' : 'NON'}`);
        } else {
            console.log('\n❌ GÉNÉRATION ÉCHOUÉE');
            console.log(`Erreur: ${response.error}`);
        }
        
        console.log('\n4️⃣ Test des statistiques...');
        
        const stats = connector.getStats();
        console.log('📊 Statistiques connecteur:');
        console.log(`   - Modèle: ${stats.config.model}`);
        console.log(`   - Requêtes totales: ${stats.metrics.totalRequests}`);
        console.log(`   - Requêtes réussies: ${stats.metrics.successfulRequests}`);
        console.log(`   - Tokens utilisés: ${stats.metrics.totalTokensUsed}`);
        console.log(`   - Temps réponse moyen: ${Math.round(stats.metrics.averageResponseTime)}ms`);
        console.log(`   - Cache: ${stats.cache.size}/${stats.cache.maxSize}`);
        console.log(`   - Möbius: ${stats.mobiusIntegration.thoughtsFromDeepSeek} pensées`);
        
        console.log('\n5️⃣ Test de performance...');
        
        const performanceTests = [
            "Quelle est la capitale de la France ?",
            "Explique-moi l'intelligence artificielle en une phrase.",
            "Quel est le sens de la vie ?"
        ];
        
        let totalTime = 0;
        let successCount = 0;
        
        for (let i = 0; i < performanceTests.length; i++) {
            const testStart = Date.now();
            const testResponse = await connector.chat(performanceTests[i], {
                temperature: 0.5,
                maxTokens: 50
            });
            const testTime = Date.now() - testStart;
            totalTime += testTime;
            
            if (testResponse.success) {
                successCount++;
                console.log(`   ✅ Test ${i+1}: ${testTime}ms - ${testResponse.content.substring(0, 50)}...`);
            } else {
                console.log(`   ❌ Test ${i+1}: ${testTime}ms - ÉCHEC`);
            }
        }
        
        const avgTime = totalTime / performanceTests.length;
        const successRate = (successCount / performanceTests.length) * 100;
        
        console.log(`\n📊 Performance globale:`);
        console.log(`   - Temps moyen: ${Math.round(avgTime)}ms`);
        console.log(`   - Taux de succès: ${successRate.toFixed(1)}%`);
        console.log(`   - Tests réussis: ${successCount}/${performanceTests.length}`);
        
        // Arrêter le connecteur
        connector.stop();
        
        console.log('\n🎉 === RÉSUMÉ TEST DEEPSEEK R1 8B ===');
        
        console.log('✅ CONFIGURATION VALIDÉE:');
        console.log('  • DeepSeek R1 8B disponible via Ollama');
        console.log('  • Connecteur fonctionnel');
        console.log('  • Intégration mémoire thermique');
        console.log('  • Système Möbius connecté');
        console.log('  • Performance satisfaisante');
        
        console.log('\n✅ AVANTAGES DEEPSEEK R1 8B:');
        console.log('  • Modèle plus puissant que 7B');
        console.log('  • Meilleure compréhension');
        console.log('  • Réponses plus nuancées');
        console.log('  • Capacités de raisonnement améliorées');
        console.log('  • Toujours local et privé');
        
        console.log('\n🚀 PRÊT POUR UTILISATION:');
        console.log('  1. Démarrer: node server-luna.js');
        console.log('  2. Interface: http://localhost:3000/luna');
        console.log('  3. DeepSeek R1 8B actif par défaut !');
        console.log('  4. Utiliser bouton "Activer DeepSeek" dans interface');
        
        if (successRate === 100 && avgTime < 5000) {
            console.log('\n🏆 PERFORMANCE EXCELLENTE - SYSTÈME OPTIMAL !');
        } else if (successRate >= 80) {
            console.log('\n✅ PERFORMANCE BONNE - SYSTÈME FONCTIONNEL !');
        } else {
            console.log('\n⚠️ PERFORMANCE À AMÉLIORER - VÉRIFIER CONFIGURATION');
        }
        
        console.log('\n✅ Test terminé avec succès !');
        
    } catch (error) {
        console.error('❌ Erreur durant le test:', error.message);
        console.log('\n📋 DIAGNOSTIC:');
        console.log('- Vérifiez qu\'Ollama est démarré: ollama serve');
        console.log('- Vérifiez que DeepSeek R1 8B est installé: ollama list');
        console.log('- Si absent, installez: ollama pull deepseek-r1:8b');
        console.log('- Vérifiez la connexion réseau locale');
    }
}

// Fonction d'aide pour afficher l'architecture
function showR1Architecture() {
    console.log('\n🏗️ === ARCHITECTURE DEEPSEEK R1 8B ===');
    console.log('📁 Structure:');
    console.log('  ├── services/');
    console.log('  │   └── deepseek-direct-connector.js  🔌 Connecteur R1 8B');
    console.log('  ├── server-luna.js                    🚀 Config R1 8B');
    console.log('  ├── views/luna-chat.ejs               🖥️ Interface');
    console.log('  └── Ollama → deepseek-r1:8b           🧠 Modèle local');
    
    console.log('\n🔄 Flux R1 8B:');
    console.log('  Interface → Connecteur → Ollama → DeepSeek R1 8B');
    console.log('  R1 8B → Mémoire Thermique → Möbius → Interface');
    
    console.log('\n⚡ Avantages R1 8B vs 7B:');
    console.log('  • +14% de paramètres');
    console.log('  • Meilleur raisonnement');
    console.log('  • Réponses plus précises');
    console.log('  • Compréhension améliorée');
}

// Exécution du test
if (require.main === module) {
    showR1Architecture();
    testDeepSeekR1_8B().catch(console.error);
}

module.exports = { testDeepSeekR1_8B };
