const EventEmitter = require('events');

/**
 * 🤖 SERVICE AGENT
 * Service de gestion des agents IA
 */
class AgentService extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            maxAgents: config.maxAgents || 10,
            defaultModel: config.defaultModel || 'deepseek-r1-8b-electron',
            autoStart: config.autoStart !== false
        };
        
        this.agents = new Map();
        this.activeAgents = new Set();
        
        this.stats = {
            totalAgents: 0,
            activeAgents: 0,
            totalRequests: 0,
            successfulRequests: 0,
            averageResponseTime: 0
        };
        
        console.log('🤖 Service Agent initialisé');
        
        if (this.config.autoStart) {
            this.initialize();
        }
    }
    
    /**
     * 🚀 Initialise le service
     */
    initialize() {
        try {
            // Créer les agents par défaut
            this.createDefaultAgents();
            
            // Démarrer le monitoring
            this.startMonitoring();
            
            this.emit('initialized');
            
        } catch (error) {
            console.error('❌ Erreur initialisation service agent:', error.message);
            this.emit('error', error);
        }
    }
    
    /**
     * 🏗️ Crée les agents par défaut
     */
    createDefaultAgents() {
        const defaultAgents = [
            {
                id: 'luna-main',
                name: 'Luna Principal',
                type: 'conversational',
                model: 'deepseek-r1-8b-electron',
                capabilities: ['chat', 'analysis', 'coding']
            },
            {
                id: 'luna-assistant',
                name: 'Luna Assistant',
                type: 'helper',
                model: 'deepseek-r1-8b-electron',
                capabilities: ['help', 'documentation', 'support']
            },
            {
                id: 'luna-analyzer',
                name: 'Luna Analyseur',
                type: 'analytical',
                model: 'deepseek-r1-8b-electron',
                capabilities: ['analysis', 'data-processing', 'insights']
            }
        ];
        
        for (const agentConfig of defaultAgents) {
            this.createAgent(agentConfig);
        }
        
        console.log(`🤖 ${defaultAgents.length} agents par défaut créés`);
    }
    
    /**
     * ➕ Crée un nouvel agent
     */
    createAgent(config) {
        try {
            const agent = {
                id: config.id || `agent_${Date.now()}`,
                name: config.name || 'Agent Sans Nom',
                type: config.type || 'general',
                model: config.model || this.config.defaultModel,
                capabilities: config.capabilities || ['chat'],
                status: 'inactive',
                created: Date.now(),
                lastActivity: null,
                requests: 0,
                successfulRequests: 0,
                averageResponseTime: 0,
                config: config.agentConfig || {}
            };
            
            this.agents.set(agent.id, agent);
            this.stats.totalAgents++;
            
            this.emit('agentCreated', agent);
            
            return agent;
            
        } catch (error) {
            console.error('❌ Erreur création agent:', error.message);
            return null;
        }
    }
    
    /**
     * ▶️ Active un agent
     */
    activateAgent(agentId) {
        try {
            const agent = this.agents.get(agentId);
            
            if (!agent) {
                throw new Error(`Agent ${agentId} non trouvé`);
            }
            
            agent.status = 'active';
            agent.lastActivity = Date.now();
            this.activeAgents.add(agentId);
            this.stats.activeAgents++;
            
            this.emit('agentActivated', agent);
            
            return agent;
            
        } catch (error) {
            console.error('❌ Erreur activation agent:', error.message);
            return null;
        }
    }
    
    /**
     * ⏸️ Désactive un agent
     */
    deactivateAgent(agentId) {
        try {
            const agent = this.agents.get(agentId);
            
            if (!agent) {
                throw new Error(`Agent ${agentId} non trouvé`);
            }
            
            agent.status = 'inactive';
            this.activeAgents.delete(agentId);
            this.stats.activeAgents--;
            
            this.emit('agentDeactivated', agent);
            
            return agent;
            
        } catch (error) {
            console.error('❌ Erreur désactivation agent:', error.message);
            return null;
        }
    }
    
    /**
     * 💬 Envoie une requête à un agent
     */
    async sendRequest(agentId, request) {
        try {
            const agent = this.agents.get(agentId);
            
            if (!agent) {
                throw new Error(`Agent ${agentId} non trouvé`);
            }
            
            if (agent.status !== 'active') {
                this.activateAgent(agentId);
            }
            
            const startTime = Date.now();
            
            // Traiter la requête
            const response = await this.processRequest(agent, request);
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            // Mettre à jour les statistiques
            agent.requests++;
            agent.lastActivity = endTime;
            
            if (response.success) {
                agent.successfulRequests++;
                this.stats.successfulRequests++;
            }
            
            // Calculer le temps de réponse moyen
            agent.averageResponseTime = (agent.averageResponseTime * (agent.requests - 1) + responseTime) / agent.requests;
            this.stats.averageResponseTime = (this.stats.averageResponseTime * (this.stats.totalRequests) + responseTime) / (this.stats.totalRequests + 1);
            
            this.stats.totalRequests++;
            
            this.emit('requestProcessed', {
                agent: agent.id,
                request,
                response,
                responseTime
            });
            
            return {
                success: true,
                agent: agent.id,
                response: response.content,
                responseTime,
                model: agent.model
            };
            
        } catch (error) {
            console.error('❌ Erreur requête agent:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 🔄 Traite une requête
     */
    async processRequest(agent, request) {
        try {
            // Utiliser le connecteur DeepSeek global si disponible
            if (global.deepSeekConnector) {
                const response = await global.deepSeekConnector.chat(request.message || request);
                return response;
            }
            
            // Réponse de fallback basée sur le type d'agent
            const responses = {
                conversational: `Réponse conversationnelle de ${agent.name}: ${request.message || request}`,
                helper: `Aide de ${agent.name}: Je peux vous aider avec "${request.message || request}"`,
                analytical: `Analyse de ${agent.name}: Voici mon analyse de "${request.message || request}"`
            };
            
            return {
                success: true,
                content: responses[agent.type] || `Réponse de ${agent.name}: ${request.message || request}`,
                model: agent.model
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 📊 Démarre le monitoring
     */
    startMonitoring() {
        setInterval(() => {
            this.updateAgentStats();
            this.cleanupInactiveAgents();
        }, 30000); // Toutes les 30 secondes
        
        console.log('📊 Monitoring des agents démarré');
    }
    
    /**
     * 📈 Met à jour les statistiques des agents
     */
    updateAgentStats() {
        for (const agent of this.agents.values()) {
            // Vérifier l'inactivité
            if (agent.lastActivity && Date.now() - agent.lastActivity > 300000) { // 5 minutes
                if (agent.status === 'active') {
                    this.deactivateAgent(agent.id);
                }
            }
        }
        
        this.emit('statsUpdated', this.getStats());
    }
    
    /**
     * 🧹 Nettoie les agents inactifs
     */
    cleanupInactiveAgents() {
        const inactiveThreshold = 3600000; // 1 heure
        const now = Date.now();
        
        for (const [agentId, agent] of this.agents.entries()) {
            if (agent.lastActivity && now - agent.lastActivity > inactiveThreshold && agent.requests === 0) {
                // Supprimer les agents jamais utilisés après 1 heure
                this.removeAgent(agentId);
            }
        }
    }
    
    /**
     * 🗑️ Supprime un agent
     */
    removeAgent(agentId) {
        try {
            const agent = this.agents.get(agentId);
            
            if (!agent) {
                return false;
            }
            
            this.deactivateAgent(agentId);
            this.agents.delete(agentId);
            this.stats.totalAgents--;
            
            this.emit('agentRemoved', agent);
            
            return true;
            
        } catch (error) {
            console.error('❌ Erreur suppression agent:', error.message);
            return false;
        }
    }
    
    /**
     * 📋 Obtient la liste des agents
     */
    getAgents() {
        return Array.from(this.agents.values());
    }
    
    /**
     * 🔍 Obtient un agent par ID
     */
    getAgent(agentId) {
        return this.agents.get(agentId);
    }
    
    /**
     * 📊 Obtient les statistiques
     */
    getStats() {
        return {
            ...this.stats,
            agents: this.getAgents().map(agent => ({
                id: agent.id,
                name: agent.name,
                type: agent.type,
                status: agent.status,
                requests: agent.requests,
                successRate: agent.requests > 0 ? agent.successfulRequests / agent.requests : 0,
                averageResponseTime: agent.averageResponseTime
            }))
        };
    }
    
    /**
     * 🔄 Redémarre tous les agents
     */
    restartAllAgents() {
        try {
            for (const agentId of this.activeAgents) {
                this.deactivateAgent(agentId);
                this.activateAgent(agentId);
            }
            
            this.emit('allAgentsRestarted');
            
            return true;
            
        } catch (error) {
            console.error('❌ Erreur redémarrage agents:', error.message);
            return false;
        }
    }
}

module.exports = AgentService;
