#!/usr/bin/env node

/**
 * 🚀 SCRIPT DE DÉMARRAGE ROBUSTE POUR LOUNA AI
 * Démarre le serveur avec gestion d'erreurs améliorée
 */

const path = require('path');
const fs = require('fs');

console.log('🚀 === DÉMARRAGE LOUNA AI SERVER ===\n');

// Vérifier que nous sommes dans le bon répertoire
const serverPath = path.join(__dirname, 'server-luna.js');

if (!fs.existsSync(serverPath)) {
    console.error('❌ Fichier server-luna.js non trouvé dans:', __dirname);
    console.log('📁 Contenu du répertoire:');
    fs.readdirSync(__dirname).forEach(file => {
        console.log(`   - ${file}`);
    });
    process.exit(1);
}

console.log('✅ Serveur trouvé:', serverPath);
console.log('📁 Répertoire de travail:', __dirname);

// Variables d'environnement
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || '3000';

console.log(`🌍 Environnement: ${process.env.NODE_ENV}`);
console.log(`🔌 Port: ${process.env.PORT}`);

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error.message);
    console.error('Stack:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée non gérée:', reason);
});

// Démarrer le serveur
console.log('\n🚀 Démarrage du serveur Luna...\n');

try {
    require('./server-luna.js');
} catch (error) {
    console.error('❌ Erreur démarrage serveur:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
}
