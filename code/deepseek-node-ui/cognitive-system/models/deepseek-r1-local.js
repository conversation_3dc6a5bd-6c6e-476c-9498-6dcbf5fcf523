const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

/**
 * 🧠 DEEPSEEK R1 8B LOCAL DIRECT
 * Inférence directe sans Ollama ni API externe
 */
class DeepSeekR1Local extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            modelPath: config.modelPath || this.findLocalModel(),
            maxTokens: config.maxTokens || 2048,
            temperature: config.temperature || 0.7,
            topP: config.topP || 0.9,
            topK: config.topK || 40,
            timeout: config.timeout || 30000,
            useGPU: config.useGPU !== false,
            threads: config.threads || 4
        };
        
        this.isInitialized = false;
        this.isProcessing = false;
        this.currentProcess = null;
        
        console.log('🧠 DeepSeek R1 8B Local Direct initialisé');
        console.log(`📁 Modèle: ${this.config.modelPath || 'RECHERCHE EN COURS...'}`);
        
        this.initialize();
    }
    
    /**
     * 🔍 Trouve le modèle DeepSeek R1 8B local
     */
    findLocalModel() {
        const possiblePaths = [
            // Chemins LM Studio
            path.join(process.env.HOME, '.cache/lm-studio/models/deepseek-ai/DeepSeek-R1-Distill-Qwen-8B-GGUF'),
            path.join(process.env.HOME, '.cache/lm-studio/models/deepseek-r1-8b'),
            
            // Chemins Hugging Face
            path.join(process.env.HOME, '.cache/huggingface/hub/models--deepseek-ai--DeepSeek-R1-Distill-Qwen-8B'),
            
            // Chemins personnalisés
            path.join(__dirname, '../models/deepseek-r1-8b'),
            path.join(process.cwd(), 'models/deepseek-r1-8b'),
            
            // Chemins système
            '/usr/local/share/models/deepseek-r1-8b',
            '/opt/models/deepseek-r1-8b'
        ];
        
        for (const modelPath of possiblePaths) {
            if (fs.existsSync(modelPath)) {
                // Chercher les fichiers .gguf ou .bin
                const files = fs.readdirSync(modelPath);
                const modelFile = files.find(f => 
                    f.endsWith('.gguf') || 
                    f.endsWith('.bin') || 
                    f.endsWith('.safetensors')
                );
                
                if (modelFile) {
                    const fullPath = path.join(modelPath, modelFile);
                    console.log(`✅ Modèle DeepSeek R1 8B trouvé: ${fullPath}`);
                    return fullPath;
                }
            }
        }
        
        console.warn('⚠️ Modèle DeepSeek R1 8B local non trouvé');
        return null;
    }
    
    /**
     * 🚀 Initialise le modèle local
     */
    async initialize() {
        try {
            // Essayer d'abord de trouver le modèle local
            if (this.config.modelPath && fs.existsSync(this.config.modelPath)) {
                // Vérifier la disponibilité de llama.cpp ou autre runtime
                const runtime = await this.findRuntime();
                if (runtime) {
                    this.runtime = runtime;
                    this.mode = 'direct';
                    this.isInitialized = true;

                    console.log('✅ DeepSeek R1 8B Local DIRECT initialisé');
                    console.log(`🔧 Runtime: ${runtime}`);
                    console.log(`📁 Modèle: ${this.config.modelPath}`);

                    this.emit('initialized');
                    return;
                }
            }

            // Fallback vers Ollama si disponible
            const ollamaAvailable = await this.checkOllama();
            if (ollamaAvailable) {
                this.mode = 'ollama';
                this.isInitialized = true;

                console.log('✅ DeepSeek R1 8B via Ollama initialisé');
                console.log('🔧 Mode: Ollama (fallback)');

                this.emit('initialized');
                return;
            }

            // Mode simulation si rien n'est disponible
            this.mode = 'simulation';
            this.isInitialized = true;

            console.log('⚠️ DeepSeek R1 8B en mode SIMULATION');
            console.log('💡 Installez le modèle local ou Ollama pour une vraie inférence');

            this.emit('initialized');

        } catch (error) {
            console.error('❌ Erreur initialisation DeepSeek R1 Local:', error.message);
            this.emit('error', error);
        }
    }

    /**
     * 🔍 Vérifie si Ollama est disponible
     */
    async checkOllama() {
        try {
            const axios = require('axios');
            const response = await axios.get('http://localhost:11434/api/tags', { timeout: 2000 });

            // Vérifier si deepseek-r1 est disponible
            const models = response.data.models || [];
            const hasDeepSeek = models.some(model =>
                model.name.includes('deepseek-r1') ||
                model.name.includes('deepseek')
            );

            if (hasDeepSeek) {
                console.log('✅ DeepSeek R1 trouvé dans Ollama');
                return true;
            } else {
                console.log('⚠️ DeepSeek R1 non trouvé dans Ollama');
                return false;
            }

        } catch (error) {
            console.log('⚠️ Ollama non disponible');
            return false;
        }
    }
    
    /**
     * 🔍 Trouve le runtime d'inférence
     */
    async findRuntime() {
        const possibleRuntimes = [
            'llama-cpp-python',
            'llama.cpp',
            'llamacpp',
            'python3'
        ];
        
        for (const runtime of possibleRuntimes) {
            try {
                const { spawn } = require('child_process');
                const process = spawn('which', [runtime]);
                
                const result = await new Promise((resolve) => {
                    let output = '';
                    process.stdout.on('data', (data) => {
                        output += data.toString();
                    });
                    
                    process.on('close', (code) => {
                        resolve(code === 0 ? output.trim() : null);
                    });
                });
                
                if (result) {
                    console.log(`✅ Runtime trouvé: ${runtime} (${result})`);
                    return runtime;
                }
            } catch (error) {
                // Continue la recherche
            }
        }
        
        return null;
    }
    
    /**
     * 💬 Génère une réponse avec DeepSeek R1 8B local
     */
    async generate(prompt, options = {}) {
        if (!this.isInitialized) {
            throw new Error('DeepSeek R1 Local non initialisé');
        }
        
        if (this.isProcessing) {
            throw new Error('Une génération est déjà en cours');
        }
        
        try {
            this.isProcessing = true;
            
            const config = {
                ...this.config,
                ...options
            };
            
            console.log(`🧠 Génération DeepSeek R1 Local: ${prompt.substring(0, 100)}...`);
            
            const response = await this.runInference(prompt, config);
            
            console.log(`✅ Réponse générée: ${response.substring(0, 100)}...`);
            
            return {
                success: true,
                content: response,
                model: 'deepseek-r1-8b-local',
                tokensUsed: this.estimateTokens(prompt + response),
                method: 'local_direct'
            };
            
        } catch (error) {
            console.error('❌ Erreur génération DeepSeek R1:', error.message);
            
            return {
                success: false,
                error: error.message,
                model: 'deepseek-r1-8b-local'
            };
            
        } finally {
            this.isProcessing = false;
        }
    }
    
    /**
     * 🔧 Exécute l'inférence directe
     */
    async runInference(prompt, config) {
        return new Promise((resolve, reject) => {
            let command, args;
            
            if (this.runtime === 'python3') {
                // Utiliser un script Python pour l'inférence
                command = 'python3';
                args = [
                    '-c',
                    this.generatePythonScript(prompt, config)
                ];
            } else {
                // Utiliser llama.cpp directement
                command = this.runtime;
                args = [
                    '-m', this.config.modelPath,
                    '-p', prompt,
                    '-n', config.maxTokens.toString(),
                    '--temp', config.temperature.toString(),
                    '--top-p', config.topP.toString(),
                    '--top-k', config.topK.toString(),
                    '-t', config.threads.toString()
                ];
                
                if (config.useGPU) {
                    args.push('-ngl', '32'); // Utiliser GPU
                }
            }
            
            const process = spawn(command, args);
            this.currentProcess = process;
            
            let output = '';
            let errorOutput = '';
            
            process.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            process.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            process.on('close', (code) => {
                this.currentProcess = null;
                
                if (code === 0) {
                    // Nettoyer la sortie
                    const cleanOutput = this.cleanOutput(output);
                    resolve(cleanOutput);
                } else {
                    reject(new Error(`Processus terminé avec le code ${code}: ${errorOutput}`));
                }
            });
            
            process.on('error', (error) => {
                this.currentProcess = null;
                reject(error);
            });
            
            // Timeout
            setTimeout(() => {
                if (this.currentProcess) {
                    this.currentProcess.kill();
                    this.currentProcess = null;
                    reject(new Error('Timeout de génération'));
                }
            }, config.timeout);
        });
    }
    
    /**
     * 🐍 Génère un script Python pour l'inférence
     */
    generatePythonScript(prompt, config) {
        return `
import sys
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
    
    # Charger le modèle et tokenizer
    model_path = "${this.config.modelPath}"
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        device_map="auto" if torch.cuda.is_available() else None
    )
    
    # Préparer l'entrée
    prompt = """${prompt.replace(/"/g, '\\"')}"""
    inputs = tokenizer(prompt, return_tensors="pt")
    
    # Générer
    with torch.no_grad():
        outputs = model.generate(
            inputs.input_ids,
            max_new_tokens=${config.maxTokens},
            temperature=${config.temperature},
            top_p=${config.topP},
            top_k=${config.topK},
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Décoder la réponse
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Extraire seulement la nouvelle partie
    new_text = response[len(prompt):].strip()
    print(new_text)
    
except ImportError:
    print("ERREUR: Transformers non installé. Installez avec: pip install transformers torch")
    sys.exit(1)
except Exception as e:
    print(f"ERREUR: {str(e)}")
    sys.exit(1)
`;
    }
    
    /**
     * 🧹 Nettoie la sortie du modèle
     */
    cleanOutput(output) {
        return output
            .replace(/^.*?(?=\w)/s, '') // Supprimer les préfixes
            .replace(/\[.*?\]/g, '') // Supprimer les balises
            .trim();
    }
    
    /**
     * 📊 Estime le nombre de tokens
     */
    estimateTokens(text) {
        return Math.ceil(text.length / 4); // Approximation
    }
    
    /**
     * 🛑 Arrête la génération en cours
     */
    stop() {
        if (this.currentProcess) {
            this.currentProcess.kill();
            this.currentProcess = null;
            this.isProcessing = false;
            console.log('🛑 Génération DeepSeek R1 arrêtée');
        }
    }
    
    /**
     * 📊 Obtient les statistiques
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            isProcessing: this.isProcessing,
            modelPath: this.config.modelPath,
            runtime: this.runtime,
            config: this.config
        };
    }
}

module.exports = DeepSeekR1Local;
