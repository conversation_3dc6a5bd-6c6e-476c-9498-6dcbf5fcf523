#!/usr/bin/env node

/**
 * 🧠 TEST DEEPSEEK R1 8B LOCAL DIRECT
 * Test de l'implémentation locale directe sans Ollama
 */

console.log('🧠 === TEST DEEPSEEK R1 8B LOCAL DIRECT ===\n');

async function testDeepSeekLocalDirect() {
    try {
        console.log('1️⃣ Test du modèle local direct...');
        
        // Charger le modèle local
        const DeepSeekR1Local = require('./cognitive-system/models/deepseek-r1-local');
        
        const localModel = new DeepSeekR1Local({
            temperature: 0.7,
            maxTokens: 100,
            useGPU: true
        });
        
        console.log('✅ Modèle DeepSeek R1 Local créé');
        
        // Attendre l'initialisation
        await new Promise((resolve, reject) => {
            localModel.on('initialized', resolve);
            localModel.on('error', reject);
            setTimeout(() => reject(new Error('Timeout initialisation')), 10000);
        });
        
        console.log('✅ Modèle DeepSeek R1 Local initialisé');
        
        // Test de génération
        console.log('\n2️⃣ Test de génération...');
        
        const testPrompt = "Bonjour, peux-tu me dire ton nom et tes capacités ?";
        console.log(`📝 Prompt: ${testPrompt}`);
        
        const response = await localModel.generate(testPrompt, {
            temperature: 0.5,
            maxTokens: 50
        });
        
        if (response.success) {
            console.log('✅ Génération réussie !');
            console.log(`🤖 Réponse: ${response.content}`);
            console.log(`📊 Tokens: ${response.tokensUsed}`);
            console.log(`🔧 Méthode: ${response.method}`);
        } else {
            console.log('❌ Génération échouée:', response.error);
        }
        
        // Test du connecteur complet
        console.log('\n3️⃣ Test du connecteur complet...');
        
        const DeepSeekDirectConnector = require('./services/deepseek-direct-connector');
        
        // Mock de mémoire thermique
        const mockThermalMemory = {
            add: (data, importance, category) => {
                console.log(`📝 Mémoire: ${category} - ${data.substring(0, 50)}...`);
                return { id: Date.now(), data, importance, category };
            },
            search: (query) => {
                console.log(`🔍 Recherche: "${query}"`);
                return [];
            },
            on: () => {},
            emit: () => {}
        };
        
        const connector = new DeepSeekDirectConnector(mockThermalMemory, {
            model: 'deepseek-r1-8b-local',
            temperature: 0.7,
            maxTokens: 100
        });
        
        console.log('✅ Connecteur DeepSeek Direct créé');
        
        // Attendre l'initialisation du connecteur
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Test de chat
        console.log('\n4️⃣ Test de chat avec connecteur...');
        
        const chatResponse = await connector.chat("Salut ! Comment ça va ?", {
            temperature: 0.6,
            maxTokens: 80
        });
        
        if (chatResponse.success) {
            console.log('✅ Chat réussi !');
            console.log(`🤖 Réponse: ${chatResponse.content}`);
            console.log(`📊 Tokens: ${chatResponse.tokensUsed}`);
            console.log(`⏱️ Temps: ${chatResponse.responseTime}ms`);
            console.log(`🧠 Intégration thermique: ${chatResponse.thermalIntegration}`);
            console.log(`🔄 Intégration Möbius: ${chatResponse.mobiusIntegration}`);
        } else {
            console.log('❌ Chat échoué:', chatResponse.error);
        }
        
        // Test des statistiques
        console.log('\n5️⃣ Test des statistiques...');
        
        const stats = connector.getStats();
        console.log('📊 Statistiques connecteur:');
        console.log(`   - Modèle: ${stats.config.model}`);
        console.log(`   - Requêtes totales: ${stats.metrics.totalRequests}`);
        console.log(`   - Requêtes réussies: ${stats.metrics.successfulRequests}`);
        console.log(`   - Tokens utilisés: ${stats.metrics.totalTokensUsed}`);
        console.log(`   - Cache: ${stats.cache.size}/${stats.cache.maxSize}`);
        console.log(`   - Möbius: ${stats.mobiusIntegration.thoughtsFromDeepSeek} pensées`);
        
        const localStats = localModel.getStats();
        console.log('\n📊 Statistiques modèle local:');
        console.log(`   - Initialisé: ${localStats.isInitialized}`);
        console.log(`   - En traitement: ${localStats.isProcessing}`);
        console.log(`   - Runtime: ${localStats.runtime}`);
        console.log(`   - Modèle: ${localStats.modelPath || 'NON TROUVÉ'}`);
        
        // Arrêter les services
        connector.stop();
        localModel.stop();
        
        console.log('\n🎉 === RÉSUMÉ TEST LOCAL DIRECT ===');
        
        console.log('✅ FONCTIONNALITÉS TESTÉES:');
        console.log('  • Modèle DeepSeek R1 8B local direct');
        console.log('  • Connecteur avec mémoire thermique');
        console.log('  • Intégration système Möbius');
        console.log('  • Cache intelligent');
        console.log('  • Métriques temps réel');
        
        console.log('\n✅ AVANTAGES LOCAL DIRECT:');
        console.log('  • Aucune dépendance Ollama');
        console.log('  • Contrôle total du modèle');
        console.log('  • Performance optimisée');
        console.log('  • Confidentialité maximale');
        console.log('  • Intégration native Electron');
        
        if (localStats.modelPath) {
            console.log('\n🚀 PRÊT POUR UTILISATION:');
            console.log('  1. Modèle local trouvé et fonctionnel');
            console.log('  2. Démarrer: node server-luna.js');
            console.log('  3. Interface: http://localhost:3000/luna');
            console.log('  4. DeepSeek R1 8B local direct actif !');
        } else {
            console.log('\n⚠️ CONFIGURATION REQUISE:');
            console.log('  1. Télécharger DeepSeek R1 8B GGUF');
            console.log('  2. Placer dans ~/.cache/lm-studio/models/');
            console.log('  3. Installer transformers: pip install transformers torch');
            console.log('  4. Redémarrer le test');
        }
        
        console.log('\n✅ Test terminé avec succès !');
        
    } catch (error) {
        console.error('❌ Erreur durant le test:', error.message);
        console.log('\n📋 DIAGNOSTIC:');
        console.log('- Vérifiez que DeepSeek R1 8B est téléchargé localement');
        console.log('- Vérifiez que Python et transformers sont installés');
        console.log('- Vérifiez les permissions d\'accès aux fichiers');
        console.log('- Vérifiez la mémoire disponible (>8GB recommandé)');
    }
}

// Fonction d'aide pour afficher l'architecture
function showLocalArchitecture() {
    console.log('\n🏗️ === ARCHITECTURE LOCAL DIRECT ===');
    console.log('📁 Structure:');
    console.log('  ├── cognitive-system/');
    console.log('  │   └── models/');
    console.log('  │       └── deepseek-r1-local.js     🧠 Modèle local');
    console.log('  ├── services/');
    console.log('  │   └── deepseek-direct-connector.js 🔌 Connecteur');
    console.log('  ├── thermal-memory/');
    console.log('  │   └── thermal-memory-complete.js   💾 Mémoire');
    console.log('  └── server-luna.js                   🚀 Serveur');
    
    console.log('\n🔄 Flux local:');
    console.log('  Interface → Connecteur → Modèle Local → Réponse');
    console.log('  Réponse → Mémoire Thermique → Möbius → Interface');
    
    console.log('\n⚡ Avantages:');
    console.log('  • Pas de dépendance externe');
    console.log('  • Latence minimale');
    console.log('  • Confidentialité totale');
    console.log('  • Contrôle complet');
}

// Exécution du test
if (require.main === module) {
    showLocalArchitecture();
    testDeepSeekLocalDirect().catch(console.error);
}

module.exports = { testDeepSeekLocalDirect };
