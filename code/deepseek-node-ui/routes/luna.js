const express = require('express');
const router = express.Router();

/**
 * 🌙 ROUTES LUNA
 * Routes pour l'interface Luna
 */

// Route principale Luna
router.get('/', (req, res) => {
    try {
        res.render('luna-chat', {
            title: 'Luna AI - Interface Principale',
            version: '2.0.0',
            timestamp: Date.now()
        });
    } catch (error) {
        console.error('❌ Erreur route Luna:', error.message);
        res.status(500).send('Erreur serveur');
    }
});

// Route API status
router.get('/status', (req, res) => {
    try {
        res.json({
            status: 'active',
            service: 'Luna AI',
            version: '2.0.0',
            timestamp: Date.now(),
            uptime: process.uptime()
        });
    } catch (error) {
        console.error('❌ Erreur status Luna:', error.message);
        res.status(500).json({ error: 'Erreur serveur' });
    }
});

// Route modèles
router.get('/models', (req, res) => {
    try {
        const models = global.availableModels || ['deepseek-r1-8b-electron'];
        const activeModel = global.selectedModel || 'deepseek-r1-8b-electron';
        
        res.json({
            success: true,
            models,
            activeModel,
            connectionStatus: {
                connected: true,
                mode: 'integrated'
            }
        });
    } catch (error) {
        console.error('❌ Erreur modèles Luna:', error.message);
        res.status(500).json({ error: 'Erreur serveur' });
    }
});

// Route chat (POST)
router.post('/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({ error: 'Message requis' });
        }
        
        // Utiliser le connecteur DeepSeek global si disponible
        if (global.deepSeekConnector) {
            const response = await global.deepSeekConnector.chat(message);
            res.json(response);
        } else {
            // Réponse de fallback
            res.json({
                success: true,
                content: `Réponse Luna pour: "${message}". Le système fonctionne en mode intégré.`,
                model: 'luna-integrated',
                timestamp: Date.now()
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur chat Luna:', error.message);
        res.status(500).json({ error: 'Erreur serveur' });
    }
});

module.exports = router;
