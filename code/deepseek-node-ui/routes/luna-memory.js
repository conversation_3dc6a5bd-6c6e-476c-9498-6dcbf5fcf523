const express = require('express');
const router = express.Router();

/**
 * 🧠 ROUTES LUNA MEMORY
 * Gestion de la mémoire thermique pour Luna
 */

// Statistiques de la mémoire
router.get('/stats', (req, res) => {
    try {
        // Obtenir les stats de la mémoire thermique globale
        const memoryStats = global.thermalMemory ? global.thermalMemory.getStats() : null;
        
        const stats = {
            success: true,
            memory: {
                isActive: !!global.thermalMemory,
                totalMemories: memoryStats ? memoryStats.memoriesCount : 0,
                totalSize: memoryStats ? memoryStats.totalSize : 0,
                categories: memoryStats ? memoryStats.categories : {},
                lastAccess: memoryStats ? memoryStats.lastAccess : Date.now()
            },
            thermal: {
                zones: global.slidingZones ? global.slidingZones.getStats().zones.length : 8,
                averageTemperature: global.temperatureRegulation ? 
                    global.temperatureRegulation.getCurrentTemperature() : 37.0,
                accelerators: global.kyberAccelerators ? 
                    global.kyberAccelerators.getStats().totalAccelerators : 5
            },
            system: {
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                timestamp: Date.now()
            }
        };
        
        res.json(stats);
        
    } catch (error) {
        console.error('❌ Erreur stats mémoire:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Ajouter une mémoire
router.post('/add', (req, res) => {
    try {
        const { data, importance = 1, category = 'general' } = req.body;
        
        if (!data) {
            return res.status(400).json({
                success: false,
                error: 'Données requises'
            });
        }
        
        if (global.thermalMemory) {
            const memory = global.thermalMemory.add(data, importance, category);
            
            res.json({
                success: true,
                message: 'Mémoire ajoutée',
                memory: {
                    id: memory.id,
                    data: memory.data,
                    importance: memory.importance,
                    category: memory.category,
                    timestamp: memory.timestamp
                }
            });
        } else {
            res.status(503).json({
                success: false,
                error: 'Mémoire thermique non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur ajout mémoire:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Rechercher dans la mémoire
router.post('/search', (req, res) => {
    try {
        const { query, limit = 10 } = req.body;
        
        if (!query) {
            return res.status(400).json({
                success: false,
                error: 'Requête de recherche requise'
            });
        }
        
        if (global.thermalMemory) {
            const results = global.thermalMemory.search(query, limit);
            
            res.json({
                success: true,
                results: results.map(memory => ({
                    id: memory.id,
                    data: memory.data,
                    importance: memory.importance,
                    category: memory.category,
                    timestamp: memory.timestamp,
                    accessCount: memory.accessCount
                })),
                total: results.length,
                query
            });
        } else {
            res.json({
                success: true,
                results: [],
                total: 0,
                query,
                message: 'Mémoire thermique non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur recherche mémoire:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Zones thermiques
router.get('/zones', (req, res) => {
    try {
        if (global.slidingZones) {
            const stats = global.slidingZones.getStats();
            
            res.json({
                success: true,
                zones: stats.zones.map(zone => ({
                    id: zone.id,
                    name: zone.name,
                    position: zone.position,
                    temperature: zone.temperature,
                    activity: zone.activity,
                    radius: zone.radius
                })),
                stats: {
                    totalZones: stats.totalZones,
                    activeZones: stats.activeZones,
                    averageTemperature: stats.averageTemperature
                }
            });
        } else {
            res.json({
                success: true,
                zones: [],
                stats: {
                    totalZones: 0,
                    activeZones: 0,
                    averageTemperature: 37.0
                },
                message: 'Zones thermiques non disponibles'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur zones thermiques:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Activer une zone thermique
router.post('/zones/:name/activate', (req, res) => {
    try {
        const { name } = req.params;
        const { intensity = 1.0 } = req.body;
        
        if (global.slidingZones) {
            const zone = global.slidingZones.activateZone(name, intensity);
            
            if (zone) {
                res.json({
                    success: true,
                    message: `Zone ${name} activée`,
                    zone: {
                        id: zone.id,
                        name: zone.name,
                        activity: zone.activity,
                        temperature: zone.temperature
                    }
                });
            } else {
                res.status(404).json({
                    success: false,
                    error: 'Zone non trouvée'
                });
            }
        } else {
            res.status(503).json({
                success: false,
                error: 'Zones thermiques non disponibles'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur activation zone:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Accélérateurs Kyber
router.get('/accelerators', (req, res) => {
    try {
        if (global.kyberAccelerators) {
            const stats = global.kyberAccelerators.getStats();
            
            res.json({
                success: true,
                accelerators: stats.accelerators.map(acc => ({
                    id: acc.id,
                    name: acc.name,
                    type: acc.type,
                    factor: acc.factor,
                    active: acc.active,
                    uses: acc.uses,
                    totalSpeedup: acc.totalSpeedup
                })),
                stats: {
                    totalAccelerations: stats.totalAccelerations,
                    averageSpeedup: stats.averageSpeedup,
                    activeAccelerators: stats.activeAccelerators
                }
            });
        } else {
            res.json({
                success: true,
                accelerators: [],
                stats: {
                    totalAccelerations: 0,
                    averageSpeedup: 1.0,
                    activeAccelerators: 0
                },
                message: 'Accélérateurs Kyber non disponibles'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur accélérateurs:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Température
router.get('/temperature', (req, res) => {
    try {
        if (global.temperatureRegulation) {
            const stats = global.temperatureRegulation.getStats();
            
            res.json({
                success: true,
                temperature: {
                    current: stats.currentTemperature,
                    target: stats.targetTemperature,
                    average: stats.averageTemperature,
                    max: stats.maxRecordedTemp,
                    min: stats.minRecordedTemp
                },
                regulation: {
                    active: stats.regulationActive,
                    cycles: stats.regulationCycles,
                    cpuTemperature: stats.cpuTemperature,
                    memoryUsage: stats.memoryUsage
                },
                history: stats.temperatureHistory
            });
        } else {
            res.json({
                success: true,
                temperature: {
                    current: 37.0,
                    target: 37.0,
                    average: 37.0,
                    max: 37.0,
                    min: 37.0
                },
                regulation: {
                    active: false,
                    cycles: 0,
                    cpuTemperature: 0,
                    memoryUsage: 0
                },
                history: [],
                message: 'Régulation de température non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur température:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Boost de température
router.post('/temperature/boost', (req, res) => {
    try {
        const { boost = 1.0, duration = 5000 } = req.body;
        
        if (global.temperatureRegulation) {
            global.temperatureRegulation.applyTemperatureBoost(boost, duration);
            
            res.json({
                success: true,
                message: 'Boost de température appliqué',
                boost,
                duration
            });
        } else {
            res.status(503).json({
                success: false,
                error: 'Régulation de température non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur boost température:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Sauvegarde de la mémoire
router.post('/save', (req, res) => {
    try {
        if (global.thermalMemory && typeof global.thermalMemory.save === 'function') {
            global.thermalMemory.save();
            
            res.json({
                success: true,
                message: 'Mémoire sauvegardée',
                timestamp: Date.now()
            });
        } else {
            res.status(503).json({
                success: false,
                error: 'Fonction de sauvegarde non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur sauvegarde mémoire:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

module.exports = router;
