const express = require('express');
const router = express.Router();

/**
 * 🧠 ROUTES DEEPSEEK DIRECT POUR ELECTRON
 * API pour la connexion directe DeepSeek → Mémoire Thermique
 */

// Variables globales pour les services
let deepSeekConnector = null;
let thermalMemory = null;
let socketIo = null;

/**
 * 🚀 Initialise les routes DeepSeek
 */
function init(services) {
    deepSeekConnector = services.deepSeekConnector || global.deepSeekConnector;
    thermalMemory = services.thermalMemory || global.thermalMemory || global.thermalMemoryComplete;
    socketIo = services.socketIo;

    console.log('🧠 Routes DeepSeek Direct initialisées');
    console.log(`   - Connecteur: ${deepSeekConnector ? 'DISPONIBLE' : 'NON DISPONIBLE'}`);
    console.log(`   - Mémoire thermique: ${thermalMemory ? 'DISPONIBLE' : 'NON DISPONIBLE'}`);
    console.log(`   - Socket.IO: ${socketIo ? 'DISPONIBLE' : 'NON DISPONIBLE'}`);
}

/**
 * 🖥️ Route pour afficher la page DeepSeek
 */
router.get('/deepseek', (req, res) => {
    res.render('luna-deepseek', {
        title: 'DeepSeek Direct - Louna AI',
        page: 'deepseek'
    });
});

/**
 * 💬 Route pour chat avec DeepSeek
 */
router.post('/deepseek/chat', async (req, res) => {
    try {
        const { message, context = {} } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        if (!deepSeekConnector) {
            return res.status(503).json({
                success: false,
                error: 'Connecteur DeepSeek non disponible'
            });
        }
        
        console.log(`🧠 Requête DeepSeek: ${message.substring(0, 100)}...`);
        
        // Envoyer la requête à DeepSeek
        const response = await deepSeekConnector.chat(message, {
            ...context,
            importance: context.importance || 0.8,
            temperature: context.temperature || 0.7
        });
        
        if (response.success) {
            // Émettre la réponse via Socket.IO si disponible
            if (socketIo) {
                socketIo.emit('deepseek response', {
                    message,
                    response: response.content,
                    model: response.model,
                    tokensUsed: response.tokensUsed,
                    responseTime: response.responseTime,
                    timestamp: Date.now()
                });
            }
            
            console.log(`✅ Réponse DeepSeek générée (${response.responseTime}ms, ${response.tokensUsed} tokens)`);
            
            res.json({
                success: true,
                response: response.content,
                model: response.model,
                tokensUsed: response.tokensUsed,
                responseTime: response.responseTime,
                thermalIntegration: response.thermalIntegration,
                mobiusIntegration: response.mobiusIntegration,
                timestamp: Date.now()
            });
        } else {
            console.error(`❌ Erreur DeepSeek: ${response.error}`);
            
            res.status(500).json({
                success: false,
                error: response.error,
                model: response.model
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur route chat DeepSeek:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * 📊 Route pour obtenir les statistiques DeepSeek
 */
router.get('/deepseek/stats', (req, res) => {
    try {
        if (!deepSeekConnector) {
            return res.status(503).json({
                success: false,
                error: 'Connecteur DeepSeek non disponible'
            });
        }
        
        const stats = deepSeekConnector.getStats();
        
        res.json({
            success: true,
            stats,
            timestamp: Date.now()
        });
        
    } catch (error) {
        console.error('❌ Erreur récupération stats DeepSeek:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * 🔧 Route pour configurer DeepSeek
 */
router.post('/deepseek/config', (req, res) => {
    try {
        const { model, temperature, maxTokens } = req.body;
        
        if (!deepSeekConnector) {
            return res.status(503).json({
                success: false,
                error: 'Connecteur DeepSeek non disponible'
            });
        }
        
        // Mettre à jour la configuration
        if (model) deepSeekConnector.config.model = model;
        if (temperature !== undefined) deepSeekConnector.config.temperature = temperature;
        if (maxTokens) deepSeekConnector.config.maxTokens = maxTokens;
        
        console.log('🔧 Configuration DeepSeek mise à jour:', { model, temperature, maxTokens });
        
        res.json({
            success: true,
            config: {
                model: deepSeekConnector.config.model,
                temperature: deepSeekConnector.config.temperature,
                maxTokens: deepSeekConnector.config.maxTokens
            },
            timestamp: Date.now()
        });
        
    } catch (error) {
        console.error('❌ Erreur configuration DeepSeek:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * 🧠 Route pour obtenir l'état de la mémoire thermique
 */
router.get('/deepseek/memory', (req, res) => {
    try {
        if (!thermalMemory) {
            return res.status(503).json({
                success: false,
                error: 'Mémoire thermique non disponible'
            });
        }
        
        let memoryStats = {};
        
        // Obtenir les statistiques selon le type de mémoire thermique
        if (global.thermalMemoryComplete && global.thermalMemoryComplete.getStats) {
            memoryStats = global.thermalMemoryComplete.getStats();
        } else if (thermalMemory.getStats) {
            memoryStats = thermalMemory.getStats();
        } else if (thermalMemory.getAllEntries) {
            const entries = thermalMemory.getAllEntries();
            const deepseekEntries = entries.filter(entry => 
                entry.category === 'deepseek_question' || entry.category === 'deepseek_response'
            );
            
            memoryStats = {
                totalEntries: entries.length,
                deepseekEntries: deepseekEntries.length,
                lastEntry: entries.length > 0 ? entries[entries.length - 1] : null
            };
        }
        
        res.json({
            success: true,
            memory: memoryStats,
            timestamp: Date.now()
        });
        
    } catch (error) {
        console.error('❌ Erreur récupération mémoire thermique:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * 🔄 Route pour obtenir l'état du système Möbius
 */
router.get('/deepseek/mobius', (req, res) => {
    try {
        let mobiusStats = null;
        
        // Vérifier le système Möbius dans la mémoire thermique complète
        if (global.thermalMemoryComplete && global.thermalMemoryComplete.getMobiusStats) {
            mobiusStats = global.thermalMemoryComplete.getMobiusStats();
        } else if (thermalMemory && thermalMemory.mobiusState) {
            mobiusStats = {
                state: thermalMemory.mobiusState,
                active: thermalMemory.mobiusState.isActive,
                cycles: thermalMemory.mobiusState.cycleCount,
                energy: thermalMemory.mobiusState.energy
            };
        }
        
        res.json({
            success: true,
            mobius: mobiusStats,
            available: !!mobiusStats,
            timestamp: Date.now()
        });
        
    } catch (error) {
        console.error('❌ Erreur récupération état Möbius:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * 🧪 Route pour tester la connexion DeepSeek
 */
router.post('/deepseek/test', async (req, res) => {
    try {
        if (!deepSeekConnector) {
            return res.status(503).json({
                success: false,
                error: 'Connecteur DeepSeek non disponible'
            });
        }
        
        console.log('🧪 Test de connexion DeepSeek...');
        
        const testResponse = await deepSeekConnector.chat('Bonjour, peux-tu confirmer que tu fonctionnes ?', {
            temperature: 0.1,
            maxTokens: 100,
            importance: 0.5
        });
        
        if (testResponse.success) {
            console.log('✅ Test DeepSeek réussi');
            
            res.json({
                success: true,
                test: 'passed',
                response: testResponse.content,
                responseTime: testResponse.responseTime,
                model: testResponse.model,
                timestamp: Date.now()
            });
        } else {
            console.error('❌ Test DeepSeek échoué:', testResponse.error);
            
            res.status(500).json({
                success: false,
                test: 'failed',
                error: testResponse.error,
                timestamp: Date.now()
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur test DeepSeek:', error);
        res.status(500).json({
            success: false,
            test: 'error',
            error: error.message,
            timestamp: Date.now()
        });
    }
});

/**
 * 🔄 Gestionnaires Socket.IO pour DeepSeek
 */
function initSocketHandlers(io) {
    socketIo = io;
    
    io.on('connection', (socket) => {
        console.log('🔌 Client connecté aux événements DeepSeek');
        
        // Chat en temps réel avec DeepSeek
        socket.on('deepseek chat', async (data) => {
            try {
                const { message, context = {} } = data;
                
                if (!deepSeekConnector) {
                    socket.emit('deepseek error', {
                        error: 'Connecteur DeepSeek non disponible'
                    });
                    return;
                }
                
                console.log(`🧠 Chat Socket DeepSeek: ${message.substring(0, 50)}...`);
                
                // Émettre le début de traitement
                socket.emit('deepseek processing', {
                    message,
                    timestamp: Date.now()
                });
                
                const response = await deepSeekConnector.chat(message, context);
                
                if (response.success) {
                    socket.emit('deepseek response', {
                        message,
                        response: response.content,
                        model: response.model,
                        tokensUsed: response.tokensUsed,
                        responseTime: response.responseTime,
                        thermalIntegration: response.thermalIntegration,
                        mobiusIntegration: response.mobiusIntegration,
                        timestamp: Date.now()
                    });
                } else {
                    socket.emit('deepseek error', {
                        message,
                        error: response.error,
                        timestamp: Date.now()
                    });
                }
                
            } catch (error) {
                console.error('❌ Erreur chat Socket DeepSeek:', error);
                socket.emit('deepseek error', {
                    error: error.message,
                    timestamp: Date.now()
                });
            }
        });
        
        // Obtenir les statistiques en temps réel
        socket.on('deepseek get stats', () => {
            try {
                if (deepSeekConnector) {
                    const stats = deepSeekConnector.getStats();
                    socket.emit('deepseek stats', {
                        success: true,
                        stats,
                        timestamp: Date.now()
                    });
                } else {
                    socket.emit('deepseek stats', {
                        success: false,
                        error: 'Connecteur DeepSeek non disponible',
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                socket.emit('deepseek stats', {
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                });
            }
        });
        
        socket.on('disconnect', () => {
            console.log('🔌 Client déconnecté des événements DeepSeek');
        });
    });
    
    console.log('🔌 Gestionnaires Socket.IO DeepSeek initialisés');
}

module.exports = {
    router,
    init,
    initSocketHandlers
};
