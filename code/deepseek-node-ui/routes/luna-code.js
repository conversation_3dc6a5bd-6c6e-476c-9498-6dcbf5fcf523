const express = require('express');
const router = express.Router();

/**
 * 💻 ROUTES LUNA CODE
 * Gestion du code pour Luna
 */

// Analyser du code
router.post('/analyze', async (req, res) => {
    try {
        const { code, language = 'javascript' } = req.body;
        
        if (!code) {
            return res.status(400).json({
                success: false,
                error: 'Code requis'
            });
        }
        
        // Analyse basique du code
        const analysis = analyzeCode(code, language);
        
        res.json({
            success: true,
            analysis,
            language,
            timestamp: Date.now()
        });
        
    } catch (error) {
        console.error('❌ Erreur analyse code:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Générer du code
router.post('/generate', async (req, res) => {
    try {
        const { description, language = 'javascript', style = 'modern' } = req.body;
        
        if (!description) {
            return res.status(400).json({
                success: false,
                error: 'Description requise'
            });
        }
        
        // Utiliser DeepSeek pour générer du code si disponible
        if (global.deepSeekConnector) {
            const prompt = `Génère du code ${language} pour: ${description}. Style: ${style}. Fournis seulement le code sans explication.`;
            
            const response = await global.deepSeekConnector.chat(prompt);
            
            res.json({
                success: true,
                code: response.content,
                language,
                description,
                style,
                model: response.model
            });
        } else {
            // Code de fallback
            const generatedCode = generateFallbackCode(description, language);
            
            res.json({
                success: true,
                code: generatedCode,
                language,
                description,
                style,
                model: 'luna-fallback'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur génération code:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Optimiser du code
router.post('/optimize', async (req, res) => {
    try {
        const { code, language = 'javascript', optimizationType = 'performance' } = req.body;
        
        if (!code) {
            return res.status(400).json({
                success: false,
                error: 'Code requis'
            });
        }
        
        // Utiliser DeepSeek pour optimiser si disponible
        if (global.deepSeekConnector) {
            const prompt = `Optimise ce code ${language} pour ${optimizationType}:\n\n${code}\n\nFournis seulement le code optimisé.`;
            
            const response = await global.deepSeekConnector.chat(prompt);
            
            res.json({
                success: true,
                originalCode: code,
                optimizedCode: response.content,
                language,
                optimizationType,
                model: response.model
            });
        } else {
            // Optimisation de fallback
            const optimizedCode = optimizeFallbackCode(code, optimizationType);
            
            res.json({
                success: true,
                originalCode: code,
                optimizedCode,
                language,
                optimizationType,
                model: 'luna-fallback'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur optimisation code:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Déboguer du code
router.post('/debug', async (req, res) => {
    try {
        const { code, language = 'javascript', error: codeError } = req.body;
        
        if (!code) {
            return res.status(400).json({
                success: false,
                error: 'Code requis'
            });
        }
        
        // Analyse des erreurs potentielles
        const debugInfo = debugCode(code, language, codeError);
        
        res.json({
            success: true,
            debugInfo,
            language,
            timestamp: Date.now()
        });
        
    } catch (error) {
        console.error('❌ Erreur debug code:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Formater du code
router.post('/format', (req, res) => {
    try {
        const { code, language = 'javascript', style = 'standard' } = req.body;
        
        if (!code) {
            return res.status(400).json({
                success: false,
                error: 'Code requis'
            });
        }
        
        // Formatage basique
        const formattedCode = formatCode(code, language, style);
        
        res.json({
            success: true,
            originalCode: code,
            formattedCode,
            language,
            style
        });
        
    } catch (error) {
        console.error('❌ Erreur formatage code:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Langages supportés
router.get('/languages', (req, res) => {
    try {
        const languages = [
            { name: 'JavaScript', id: 'javascript', extensions: ['.js', '.mjs'] },
            { name: 'TypeScript', id: 'typescript', extensions: ['.ts'] },
            { name: 'Python', id: 'python', extensions: ['.py'] },
            { name: 'Java', id: 'java', extensions: ['.java'] },
            { name: 'C++', id: 'cpp', extensions: ['.cpp', '.cc'] },
            { name: 'C#', id: 'csharp', extensions: ['.cs'] },
            { name: 'Go', id: 'go', extensions: ['.go'] },
            { name: 'Rust', id: 'rust', extensions: ['.rs'] },
            { name: 'PHP', id: 'php', extensions: ['.php'] },
            { name: 'Ruby', id: 'ruby', extensions: ['.rb'] }
        ];
        
        res.json({
            success: true,
            languages,
            total: languages.length
        });
        
    } catch (error) {
        console.error('❌ Erreur langages:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * 🔍 Analyse le code
 */
function analyzeCode(code, language) {
    const lines = code.split('\n');
    const analysis = {
        lines: lines.length,
        characters: code.length,
        functions: 0,
        variables: 0,
        comments: 0,
        complexity: 'low',
        issues: []
    };
    
    // Analyse basique selon le langage
    if (language === 'javascript') {
        analysis.functions = (code.match(/function\s+\w+|=>\s*{|const\s+\w+\s*=/g) || []).length;
        analysis.variables = (code.match(/(?:let|const|var)\s+\w+/g) || []).length;
        analysis.comments = (code.match(/\/\/.*|\/\*[\s\S]*?\*\//g) || []).length;
        
        // Détection d'issues basiques
        if (code.includes('var ')) {
            analysis.issues.push('Utilisation de "var" déconseillée, préférer "let" ou "const"');
        }
        if (code.includes('==') && !code.includes('===')) {
            analysis.issues.push('Utilisation de "==" au lieu de "==="');
        }
    }
    
    // Complexité basée sur la longueur et les structures
    if (lines.length > 100 || analysis.functions > 10) {
        analysis.complexity = 'high';
    } else if (lines.length > 50 || analysis.functions > 5) {
        analysis.complexity = 'medium';
    }
    
    return analysis;
}

/**
 * 🔧 Génère du code de fallback
 */
function generateFallbackCode(description, language) {
    const templates = {
        javascript: `// ${description}
function solution() {
    // TODO: Implémenter ${description}
    console.log('${description}');
    return true;
}

module.exports = solution;`,
        
        python: `# ${description}
def solution():
    """${description}"""
    print("${description}")
    return True

if __name__ == "__main__":
    solution()`,
        
        java: `// ${description}
public class Solution {
    public static void main(String[] args) {
        // TODO: Implémenter ${description}
        System.out.println("${description}");
    }
}`
    };
    
    return templates[language] || templates.javascript;
}

/**
 * ⚡ Optimise le code de fallback
 */
function optimizeFallbackCode(code, optimizationType) {
    // Optimisation basique
    let optimized = code;
    
    if (optimizationType === 'performance') {
        optimized = `// Code optimisé pour la performance\n${code}`;
    } else if (optimizationType === 'memory') {
        optimized = `// Code optimisé pour la mémoire\n${code}`;
    } else if (optimizationType === 'readability') {
        optimized = `// Code optimisé pour la lisibilité\n${code}`;
    }
    
    return optimized;
}

/**
 * 🐛 Débogue le code
 */
function debugCode(code, language, error) {
    const debugInfo = {
        potentialIssues: [],
        suggestions: [],
        errorAnalysis: null
    };
    
    // Analyse des erreurs communes
    if (language === 'javascript') {
        if (code.includes('undefined')) {
            debugInfo.potentialIssues.push('Variable potentiellement non définie');
        }
        if (code.includes('null')) {
            debugInfo.potentialIssues.push('Valeur null détectée');
        }
        if (!code.includes('try') && !code.includes('catch')) {
            debugInfo.suggestions.push('Considérer l\'ajout de gestion d\'erreurs');
        }
    }
    
    if (error) {
        debugInfo.errorAnalysis = {
            message: error,
            type: 'runtime',
            suggestions: ['Vérifier les variables', 'Ajouter des logs', 'Tester les cas limites']
        };
    }
    
    return debugInfo;
}

/**
 * 🎨 Formate le code
 */
function formatCode(code, language, style) {
    // Formatage basique
    let formatted = code;
    
    if (language === 'javascript') {
        // Indentation basique
        formatted = formatted.replace(/\n\s*/g, '\n  ');
        formatted = formatted.replace(/{\s*/g, ' {\n  ');
        formatted = formatted.replace(/}\s*/g, '\n}\n');
    }
    
    return formatted;
}

module.exports = router;
