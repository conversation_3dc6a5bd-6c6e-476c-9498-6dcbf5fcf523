const express = require('express');
const router = express.Router();

/**
 * 💬 ROUTES LUNA PROMPTS
 * Gestion des prompts pour Luna
 */

// Prompts prédéfinis
const predefinedPrompts = [
    {
        id: 'greeting',
        title: 'Salutation',
        prompt: 'Bonjour ! Comment allez-vous aujourd\'hui ?',
        category: 'social'
    },
    {
        id: 'capabilities',
        title: 'Capacités',
        prompt: 'Quelles sont vos principales capacités et fonctionnalités ?',
        category: 'info'
    },
    {
        id: 'help',
        title: 'Aide',
        prompt: 'Pouvez-vous m\'aider avec une tâche spécifique ?',
        category: 'assistance'
    },
    {
        id: 'creative',
        title: 'Créativité',
        prompt: 'Écrivez-moi une courte histoire créative.',
        category: 'creative'
    },
    {
        id: 'analysis',
        title: 'Analyse',
        prompt: 'Analysez ce problème et proposez des solutions.',
        category: 'analysis'
    }
];

// Liste des prompts
router.get('/', (req, res) => {
    try {
        const { category } = req.query;
        
        let prompts = predefinedPrompts;
        
        if (category) {
            prompts = prompts.filter(p => p.category === category);
        }
        
        res.json({
            success: true,
            prompts,
            total: prompts.length,
            categories: [...new Set(predefinedPrompts.map(p => p.category))]
        });
        
    } catch (error) {
        console.error('❌ Erreur liste prompts:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Obtenir un prompt spécifique
router.get('/:id', (req, res) => {
    try {
        const { id } = req.params;
        const prompt = predefinedPrompts.find(p => p.id === id);
        
        if (!prompt) {
            return res.status(404).json({
                success: false,
                error: 'Prompt non trouvé'
            });
        }
        
        res.json({
            success: true,
            prompt
        });
        
    } catch (error) {
        console.error('❌ Erreur prompt:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Exécuter un prompt
router.post('/:id/execute', async (req, res) => {
    try {
        const { id } = req.params;
        const { context } = req.body;
        
        const prompt = predefinedPrompts.find(p => p.id === id);
        
        if (!prompt) {
            return res.status(404).json({
                success: false,
                error: 'Prompt non trouvé'
            });
        }
        
        let finalPrompt = prompt.prompt;
        
        // Ajouter le contexte si fourni
        if (context) {
            finalPrompt += `\n\nContexte: ${context}`;
        }
        
        // Utiliser le connecteur DeepSeek si disponible
        if (global.deepSeekConnector) {
            const response = await global.deepSeekConnector.chat(finalPrompt);
            
            res.json({
                success: true,
                prompt: prompt.title,
                response: response.content,
                model: response.model,
                tokensUsed: response.tokensUsed
            });
        } else {
            // Réponse de fallback
            res.json({
                success: true,
                prompt: prompt.title,
                response: `Réponse pour le prompt "${prompt.title}": ${finalPrompt}`,
                model: 'luna-fallback',
                tokensUsed: 0
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur exécution prompt:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Créer un prompt personnalisé
router.post('/custom', async (req, res) => {
    try {
        const { prompt, context } = req.body;
        
        if (!prompt) {
            return res.status(400).json({
                success: false,
                error: 'Prompt requis'
            });
        }
        
        let finalPrompt = prompt;
        
        // Ajouter le contexte si fourni
        if (context) {
            finalPrompt += `\n\nContexte: ${context}`;
        }
        
        // Utiliser le connecteur DeepSeek si disponible
        if (global.deepSeekConnector) {
            const response = await global.deepSeekConnector.chat(finalPrompt);
            
            res.json({
                success: true,
                prompt: 'Custom',
                response: response.content,
                model: response.model,
                tokensUsed: response.tokensUsed
            });
        } else {
            // Réponse de fallback
            res.json({
                success: true,
                prompt: 'Custom',
                response: `Réponse pour le prompt personnalisé: ${finalPrompt}`,
                model: 'luna-fallback',
                tokensUsed: 0
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur prompt personnalisé:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Catégories de prompts
router.get('/categories/list', (req, res) => {
    try {
        const categories = [...new Set(predefinedPrompts.map(p => p.category))];
        const categoriesWithCounts = categories.map(cat => ({
            name: cat,
            count: predefinedPrompts.filter(p => p.category === cat).length,
            prompts: predefinedPrompts.filter(p => p.category === cat).map(p => ({
                id: p.id,
                title: p.title
            }))
        }));
        
        res.json({
            success: true,
            categories: categoriesWithCounts,
            total: categories.length
        });
        
    } catch (error) {
        console.error('❌ Erreur catégories:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

module.exports = router;
