const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

/**
 * 📄 ROUTES LUNA DOCUMENTS
 * Gestion des documents pour Luna
 */

// Liste des documents
router.get('/', (req, res) => {
    try {
        const documentsPath = path.join(__dirname, '../data/documents');
        
        // Créer le répertoire s'il n'existe pas
        if (!fs.existsSync(documentsPath)) {
            fs.mkdirSync(documentsPath, { recursive: true });
        }
        
        // Lister les documents
        const files = fs.readdirSync(documentsPath);
        const documents = files
            .filter(file => file.endsWith('.json') || file.endsWith('.md') || file.endsWith('.txt'))
            .map(file => {
                const filePath = path.join(documentsPath, file);
                const stats = fs.statSync(filePath);
                
                return {
                    name: file,
                    path: file,
                    size: stats.size,
                    created: stats.birthtime,
                    modified: stats.mtime,
                    type: path.extname(file).substring(1)
                };
            });
        
        res.json({
            success: true,
            documents,
            total: documents.length
        });
        
    } catch (error) {
        console.error('❌ Erreur liste documents:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur',
            documents: []
        });
    }
});

// Créer un document
router.post('/', (req, res) => {
    try {
        const { name, content, type = 'txt' } = req.body;
        
        if (!name || !content) {
            return res.status(400).json({
                success: false,
                error: 'Nom et contenu requis'
            });
        }
        
        const documentsPath = path.join(__dirname, '../data/documents');
        
        // Créer le répertoire s'il n'existe pas
        if (!fs.existsSync(documentsPath)) {
            fs.mkdirSync(documentsPath, { recursive: true });
        }
        
        const fileName = `${name}.${type}`;
        const filePath = path.join(documentsPath, fileName);
        
        // Vérifier si le fichier existe déjà
        if (fs.existsSync(filePath)) {
            return res.status(409).json({
                success: false,
                error: 'Document déjà existant'
            });
        }
        
        // Écrire le fichier
        fs.writeFileSync(filePath, content, 'utf8');
        
        res.json({
            success: true,
            message: 'Document créé',
            document: {
                name: fileName,
                path: fileName,
                size: content.length,
                created: new Date(),
                type
            }
        });
        
    } catch (error) {
        console.error('❌ Erreur création document:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Lire un document
router.get('/:name', (req, res) => {
    try {
        const { name } = req.params;
        const documentsPath = path.join(__dirname, '../data/documents');
        const filePath = path.join(documentsPath, name);
        
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                error: 'Document non trouvé'
            });
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        const stats = fs.statSync(filePath);
        
        res.json({
            success: true,
            document: {
                name,
                content,
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                type: path.extname(name).substring(1)
            }
        });
        
    } catch (error) {
        console.error('❌ Erreur lecture document:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Mettre à jour un document
router.put('/:name', (req, res) => {
    try {
        const { name } = req.params;
        const { content } = req.body;
        
        if (!content) {
            return res.status(400).json({
                success: false,
                error: 'Contenu requis'
            });
        }
        
        const documentsPath = path.join(__dirname, '../data/documents');
        const filePath = path.join(documentsPath, name);
        
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                error: 'Document non trouvé'
            });
        }
        
        // Sauvegarder l'ancien contenu
        const oldContent = fs.readFileSync(filePath, 'utf8');
        const backupPath = path.join(documentsPath, `${name}.backup.${Date.now()}`);
        fs.writeFileSync(backupPath, oldContent, 'utf8');
        
        // Écrire le nouveau contenu
        fs.writeFileSync(filePath, content, 'utf8');
        
        const stats = fs.statSync(filePath);
        
        res.json({
            success: true,
            message: 'Document mis à jour',
            document: {
                name,
                size: stats.size,
                modified: stats.mtime,
                backup: `${name}.backup.${Date.now()}`
            }
        });
        
    } catch (error) {
        console.error('❌ Erreur mise à jour document:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Supprimer un document
router.delete('/:name', (req, res) => {
    try {
        const { name } = req.params;
        const documentsPath = path.join(__dirname, '../data/documents');
        const filePath = path.join(documentsPath, name);
        
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                error: 'Document non trouvé'
            });
        }
        
        // Créer une sauvegarde avant suppression
        const content = fs.readFileSync(filePath, 'utf8');
        const trashPath = path.join(documentsPath, 'trash');
        
        if (!fs.existsSync(trashPath)) {
            fs.mkdirSync(trashPath, { recursive: true });
        }
        
        const backupFile = path.join(trashPath, `${name}.deleted.${Date.now()}`);
        fs.writeFileSync(backupFile, content, 'utf8');
        
        // Supprimer le fichier
        fs.unlinkSync(filePath);
        
        res.json({
            success: true,
            message: 'Document supprimé',
            backup: backupFile
        });
        
    } catch (error) {
        console.error('❌ Erreur suppression document:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

// Rechercher dans les documents
router.post('/search', (req, res) => {
    try {
        const { query, type } = req.body;
        
        if (!query) {
            return res.status(400).json({
                success: false,
                error: 'Requête de recherche requise'
            });
        }
        
        const documentsPath = path.join(__dirname, '../data/documents');
        
        if (!fs.existsSync(documentsPath)) {
            return res.json({
                success: true,
                results: [],
                total: 0
            });
        }
        
        const files = fs.readdirSync(documentsPath);
        const results = [];
        
        for (const file of files) {
            if (type && !file.endsWith(`.${type}`)) continue;
            
            const filePath = path.join(documentsPath, file);
            
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                const lowerContent = content.toLowerCase();
                const lowerQuery = query.toLowerCase();
                
                if (lowerContent.includes(lowerQuery)) {
                    const stats = fs.statSync(filePath);
                    
                    // Trouver les lignes correspondantes
                    const lines = content.split('\n');
                    const matchingLines = lines
                        .map((line, index) => ({ line, number: index + 1 }))
                        .filter(({ line }) => line.toLowerCase().includes(lowerQuery))
                        .slice(0, 5); // Limiter à 5 résultats par fichier
                    
                    results.push({
                        name: file,
                        path: file,
                        size: stats.size,
                        modified: stats.mtime,
                        matches: matchingLines.length,
                        preview: matchingLines
                    });
                }
            } catch (error) {
                // Ignorer les fichiers non lisibles
            }
        }
        
        // Trier par nombre de correspondances
        results.sort((a, b) => b.matches - a.matches);
        
        res.json({
            success: true,
            results,
            total: results.length,
            query
        });
        
    } catch (error) {
        console.error('❌ Erreur recherche documents:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

module.exports = router;
