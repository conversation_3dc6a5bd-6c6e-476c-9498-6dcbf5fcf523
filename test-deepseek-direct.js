#!/usr/bin/env node

/**
 * 🧠 TEST CONNEXION DIRECTE DEEPSEEK R1 8B → MÉMOIRE THERMIQUE
 * Test sans Ollama pour performance maximale
 */

console.log('🧠 === TEST DEEPSEEK R1 8B DIRECT → MÉMOIRE THERMIQUE ===\n');

async function testDeepSeekDirect() {
    try {
        console.log('1️⃣ Initialisation du système...');
        
        // Charger la mémoire thermique
        const ThermalMemory = require('./thermal-memory-complete');
        const thermalMemory = new ThermalMemory();
        
        console.log('✅ Mémoire thermique chargée');
        
        // Attendre l'initialisation
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Charger le connecteur DeepSeek Direct
        const DeepSeekDirectConnector = require('./modules/deepseek-direct-connector');
        
        console.log('2️⃣ Initialisation connecteur DeepSeek Direct...');
        
        const deepseekConnector = new DeepSeekDirectConnector(thermalMemory, {
            apiKey: 'sk-e610204de16048e09341db5489600e5d', // Clé depuis config
            model: 'deepseek-r1',
            temperature: 0.7,
            maxTokens: 1024
        });
        
        // Attendre l'initialisation du connecteur
        await new Promise((resolve, reject) => {
            deepseekConnector.on('initialized', resolve);
            deepseekConnector.on('error', reject);
            setTimeout(() => reject(new Error('Timeout initialisation')), 10000);
        });
        
        console.log('✅ Connecteur DeepSeek Direct initialisé');
        
        // Vérifier les statistiques initiales
        console.log('\n3️⃣ Statistiques initiales...');
        const initialStats = deepseekConnector.getStats();
        console.log(`🤖 Modèle: ${initialStats.config.model}`);
        console.log(`🔑 API Key: ${initialStats.config.hasApiKey ? 'CONFIGURÉE' : 'MANQUANTE'}`);
        console.log(`🌐 URL: ${initialStats.config.apiUrl}`);
        console.log(`📊 Cache: ${initialStats.cache.size}/${initialStats.cache.maxSize}`);
        console.log(`🔄 Intégration Möbius: ${initialStats.mobiusIntegration.enabled ? 'ACTIVÉE' : 'DÉSACTIVÉE'}`);
        
        // Test de conversation simple
        console.log('\n4️⃣ Test conversation DeepSeek R1...');
        
        const testQuestions = [
            'Bonjour, peux-tu te présenter ?',
            'Explique-moi le système de mémoire thermique',
            'Comment fonctionne le cycle Möbius de génération de pensées ?'
        ];
        
        let successCount = 0;
        let totalResponseTime = 0;
        
        for (let i = 0; i < testQuestions.length; i++) {
            const question = testQuestions[i];
            console.log(`\n📝 Question ${i + 1}: ${question}`);
            
            const startTime = Date.now();
            const response = await deepseekConnector.chat(question, {
                importance: 0.8,
                temperature: 0.7
            });
            const responseTime = Date.now() - startTime;
            
            if (response.success) {
                successCount++;
                totalResponseTime += responseTime;
                
                console.log(`✅ Réponse (${responseTime}ms): ${response.content.substring(0, 150)}...`);
                console.log(`📊 Tokens utilisés: ${response.tokensUsed}`);
                console.log(`🧠 Intégration thermique: ${response.thermalIntegration ? 'OUI' : 'NON'}`);
                console.log(`🔄 Intégration Möbius: ${response.mobiusIntegration ? 'OUI' : 'NON'}`);
            } else {
                console.log(`❌ Erreur: ${response.error}`);
            }
            
            // Pause entre les questions
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // Vérifier l'intégration avec la mémoire thermique
        console.log('\n5️⃣ Vérification intégration mémoire thermique...');
        
        const memoryEntries = thermalMemory.getAllEntries();
        const deepseekEntries = memoryEntries.filter(entry => 
            entry.category === 'deepseek_question' || entry.category === 'deepseek_response'
        );
        
        console.log(`💾 Entrées totales mémoire: ${memoryEntries.length}`);
        console.log(`🧠 Entrées DeepSeek: ${deepseekEntries.length}`);
        
        if (deepseekEntries.length > 0) {
            console.log('\n📝 Exemples d\'entrées DeepSeek:');
            deepseekEntries.slice(0, 3).forEach((entry, index) => {
                console.log(`   ${index + 1}. [${entry.category}] ${entry.data.substring(0, 80)}...`);
            });
        }
        
        // Test de recherche dans la mémoire
        console.log('\n6️⃣ Test recherche mémoire thermique...');
        
        const searchResults = thermalMemory.search('DeepSeek');
        console.log(`🔍 Résultats recherche "DeepSeek": ${searchResults.length}`);
        
        // Vérifier l'intégration Möbius
        console.log('\n7️⃣ Vérification intégration Möbius...');
        
        if (thermalMemory.mobiusState) {
            console.log(`🔄 Système Möbius: ${thermalMemory.mobiusState.isActive ? 'ACTIF' : 'INACTIF'}`);
            console.log(`🧠 Cycles complétés: ${thermalMemory.mobiusState.cycleCount}`);
            
            const mobiusStats = thermalMemory.getMobiusStats();
            if (mobiusStats) {
                console.log(`💭 Pensées générées: ${mobiusStats.metrics.thoughtsGenerated}`);
                console.log(`🤔 Réflexions complétées: ${mobiusStats.metrics.reflectionsCompleted}`);
            }
        } else {
            console.log('⚠️ Système Möbius non détecté');
        }
        
        // Statistiques finales
        console.log('\n8️⃣ Statistiques finales...');
        
        const finalStats = deepseekConnector.getStats();
        console.log(`📊 Requêtes totales: ${finalStats.metrics.totalRequests}`);
        console.log(`✅ Requêtes réussies: ${finalStats.metrics.successfulRequests}`);
        console.log(`❌ Requêtes échouées: ${finalStats.metrics.failedRequests}`);
        console.log(`⚡ Temps réponse moyen: ${finalStats.metrics.averageResponseTime.toFixed(0)}ms`);
        console.log(`🎯 Tokens utilisés: ${finalStats.metrics.totalTokensUsed}`);
        console.log(`💾 Intégrations mémoire: ${finalStats.metrics.memoryIntegrations}`);
        console.log(`🔄 Pensées vers Möbius: ${finalStats.mobiusIntegration.thoughtsFromDeepSeek}`);
        
        // Test de performance
        console.log('\n9️⃣ Test de performance...');
        
        const performanceTest = await deepseekConnector.chat(
            'Calcule 2+2 et explique le résultat',
            { temperature: 0.1, maxTokens: 100 }
        );
        
        if (performanceTest.success) {
            console.log(`⚡ Test performance: ${performanceTest.responseTime}ms`);
            console.log(`🧮 Réponse: ${performanceTest.content}`);
        }
        
        // Arrêter le connecteur
        deepseekConnector.stop();
        
        // Résumé final
        console.log('\n🎉 === RÉSUMÉ CONNEXION DEEPSEEK DIRECT ===');
        
        const successRate = (successCount / testQuestions.length) * 100;
        const avgResponseTime = totalResponseTime / successCount;
        
        if (successRate >= 100 && deepseekEntries.length > 0) {
            console.log('🎉 CONNEXION DIRECTE DEEPSEEK → MÉMOIRE THERMIQUE RÉUSSIE !');
            console.log('✅ API DeepSeek R1 8B: FONCTIONNELLE');
            console.log('✅ Connexion directe: SANS OLLAMA');
            console.log('✅ Intégration mémoire thermique: COMPLÈTE');
            console.log('✅ Intégration système Möbius: ACTIVE');
            console.log('✅ Cache intelligent: OPÉRATIONNEL');
            console.log('✅ Métriques temps réel: DISPONIBLES');
            
            console.log('\n📊 PERFORMANCES:');
            console.log(`⚡ Taux de succès: ${successRate.toFixed(1)}%`);
            console.log(`🚀 Temps réponse moyen: ${avgResponseTime.toFixed(0)}ms`);
            console.log(`💾 Entrées stockées: ${deepseekEntries.length}`);
            console.log(`🔍 Recherche fonctionnelle: ${searchResults.length > 0 ? 'OUI' : 'NON'}`);
            
            console.log('\n🔄 AVANTAGES CONNEXION DIRECTE:');
            console.log('🚀 Performance supérieure (pas de proxy Ollama)');
            console.log('🧠 Intégration native mémoire thermique');
            console.log('🔄 Synchronisation temps réel avec Möbius');
            console.log('📊 Métriques détaillées et cache intelligent');
            console.log('🌡️ Adaptation dynamique à la température');
            
        } else {
            console.log('⚠️ CONNEXION PARTIELLEMENT FONCTIONNELLE');
            console.log(`📊 Taux de succès: ${successRate.toFixed(1)}%`);
            console.log(`💾 Entrées stockées: ${deepseekEntries.length}`);
            
            if (successRate < 100) {
                console.log('❌ Problèmes de connexion API détectés');
            }
            if (deepseekEntries.length === 0) {
                console.log('❌ Intégration mémoire thermique incomplète');
            }
        }
        
        console.log('\n✅ Test terminé avec succès');
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Erreur durant le test:', error.message);
        console.log('\n📋 DIAGNOSTIC:');
        console.log('- Vérifiez la clé API DeepSeek');
        console.log('- Vérifiez la connexion Internet');
        console.log('- Vérifiez que la mémoire thermique fonctionne');
        console.log('- Vérifiez les logs d\'erreur ci-dessus');
        process.exit(1);
    }
}

// Fonction d'aide pour afficher les avantages
function showDirectConnectionBenefits() {
    console.log('\n🧠 === AVANTAGES CONNEXION DIRECTE DEEPSEEK ===');
    console.log('🚀 PERFORMANCE:');
    console.log('   • Pas de proxy Ollama = latence réduite');
    console.log('   • Connexion directe API = vitesse maximale');
    console.log('   • Cache intelligent = réponses instantanées');
    console.log('   • Métriques temps réel = optimisation continue');
    
    console.log('\n🧠 INTÉGRATION:');
    console.log('   • Mémoire thermique native = stockage automatique');
    console.log('   • Système Möbius = pensées enrichies');
    console.log('   • Adaptation température = réponses contextuelles');
    console.log('   • Événements temps réel = synchronisation parfaite');
    
    console.log('\n🔧 FONCTIONNALITÉS:');
    console.log('   • DeepSeek R1 8B = raisonnement avancé');
    console.log('   • Enrichissement contextuel = réponses pertinentes');
    console.log('   • Gestion d\'erreurs = robustesse maximale');
    console.log('   • Configuration flexible = adaptation facile');
}

// Exécution du test
if (require.main === module) {
    showDirectConnectionBenefits();
    testDeepSeekDirect().catch(console.error);
}

module.exports = { testDeepSeekDirect };
