{"timestamp": "2025-05-29T11:23:56.251Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.96, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1805905304174937, "accessFactor": 1.05, "decayFactor": 0.9845861456448649, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10049999999999999, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.2814788628087289, "curiosity": 1, "confidence": -0.4782298044992004, "energy": 1, "focus": 0.9356396123506455, "creativity": 1, "stress": 0.451555738049742, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748517801290, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03031101246864, "neuronActivity": 0.5155506234323002}, "emotionalHistory": [{"timestamp": 1748517722172, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5155506234323002}, {"timestamp": 1748517746383, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01515550623432, "neuronActivity": 0.5155506234323002}, {"timestamp": 1748517774682, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01515550623432, "neuronActivity": 0.5155506234323002}, {"timestamp": 1748517801290, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03031101246864, "neuronActivity": 0.5155506234323002}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:22:02 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:22:26 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:22:54 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "7:23:21 AM", "mood": "curious"}], "circadianRhythm": 0.5796071269873742, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.9602221108575592, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.8694743841871884, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9353897501035262, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.8610453073433912, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748517715358, "expiresAt": 1748518315358}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.9108402646709327, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748517715358, "expiresAt": 1748518315358}, "memory_compression_engine_1748517715359": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.4, "stability": 0.8773975791917211, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "memory_cache_optimizer_1748517715359": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.64, "stability": 0.9041875523274998, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "memory_garbage_collector_1748517715359": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.45, "stability": 0.8912474136682916, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.9237546857187899, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748517716276, "expiresAt": 1748518316276}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.9602221108575592, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.8694743841871884, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9353897501035262, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.8610453073433912, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748517715358, "expiresAt": 1748518315358}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.9108402646709327, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748517715358, "expiresAt": 1748518315358}, "memory_compression_engine_1748517715359": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.4, "stability": 0.8773975791917211, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "memory_cache_optimizer_1748517715359": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.64, "stability": 0.9041875523274998, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "memory_garbage_collector_1748517715359": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.45, "stability": 0.8912474136682916, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.9237546857187899, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748517716276, "expiresAt": 1748518316276}}, "totalAccelerators": 9, "activeAccelerators": 9, "averageBoostFactor": 2.5059444444444443, "efficiency": 0.8503566666666666, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748517774684, "totalBoostPower": 22.5535, "averageEnergy": 1, "averageStability": 0.9037287831187667, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748517774684, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.357958984375, "responseTime": 384.8707770050101, "overall": 0}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}