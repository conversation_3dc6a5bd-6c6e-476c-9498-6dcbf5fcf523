{"timestamp": "2025-05-29T11:35:00.073Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.96, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.1777786661795326, "accessFactor": 1.05, "decayFactor": 0.9848110587488641, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10049999999999999, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.10768190236287323, "curiosity": 1, "confidence": -0.7278735360070143, "energy": 1, "focus": 0.9852907353938061, "creativity": 1, "stress": 0.6327205046029106, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748518489800, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03063442080352, "neuronActivity": 0.5317210401760929}, "emotionalHistory": [{"timestamp": 1748518415418, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5317210401760929}, {"timestamp": 1748518437288, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01531721040176, "neuronActivity": 0.5317210401760929}, {"timestamp": 1748518452289, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01531721040176, "neuronActivity": 0.5317210401760929}, {"timestamp": 1748518469318, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03063442080352, "neuronActivity": 0.5317210401760929}, {"timestamp": 1748518489800, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03063442080352, "neuronActivity": 0.5317210401760929}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:33:35 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "7:33:57 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "7:34:12 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:34:29 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "7:34:49 AM", "mood": "curious"}], "circadianRhythm": 0.554802100483233, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.9126504753306648, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.8928728190260581, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9895588588738472, "energy": 1, "enabled": true, "type": "connection"}, "compression_basic_compressor_1748518398221": {"name": "Compresseur <PERSON>", "description": "Compression rapide pour données courantes", "boostFactor": 1.5, "stability": 0.8622404752168974, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518398221, "expiresAt": null}, "compression_advanced_compressor_1748518398243": {"name": "Compresseur <PERSON>", "description": "Compression haute efficacité avec algorithmes optimisés", "boostFactor": 1.5, "stability": 0.9350683609175002, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518398243, "expiresAt": null}, "compression_ultra_compressor_1748518398247": {"name": "Compresseur Ultra", "description": "Compression maximale pour situations critiques", "boostFactor": 1.5, "stability": 0.9373882138964214, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518398247, "expiresAt": null}, "compression_fast_decompressor_1748518399279": {"name": "Décompresseur Rapide", "description": "Décompression ultra-rapide pour accès fréquents", "boostFactor": 1.5, "stability": 0.882142885225373, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518399279, "expiresAt": null}, "compression_thermal_compressor_1748518407548": {"name": "Compresseur <PERSON><PERSON>", "description": "Compression spécialisée pour mémoire thermique", "boostFactor": 1.5, "stability": 0.9207613074750631, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518407548, "expiresAt": null}, "compression_predictive_compressor_1748518408621": {"name": "Compresseur <PERSON><PERSON><PERSON>", "description": "Compression intelligente basée sur les patterns d'usage", "boostFactor": 1.5, "stability": 0.8644309572327502, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518408621, "expiresAt": null}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.8646247071805555, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748518409321, "expiresAt": 1748519009321}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.9125028344394805, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748518409321, "expiresAt": 1748519009321}, "memory_compression_engine_1748518409322": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.4, "stability": 0.9014468397403291, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "memory_cache_optimizer_1748518409322": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.64, "stability": 0.8960911451969946, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "memory_garbage_collector_1748518409322": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.45, "stability": 0.9071515673866422, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.8631196578831641, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748518410119, "expiresAt": 1748519010119}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.944, "stability": 0.9126504753306648, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.583, "stability": 0.8928728190260581, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0415, "stability": 0.9895588588738472, "energy": 1, "enabled": true, "type": "connection"}, "compression_basic_compressor_1748518398221": {"name": "Compresseur <PERSON>", "description": "Compression rapide pour données courantes", "boostFactor": 1.5, "stability": 0.8622404752168974, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518398221, "expiresAt": null}, "compression_advanced_compressor_1748518398243": {"name": "Compresseur <PERSON>", "description": "Compression haute efficacité avec algorithmes optimisés", "boostFactor": 1.5, "stability": 0.9350683609175002, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518398243, "expiresAt": null}, "compression_ultra_compressor_1748518398247": {"name": "Compresseur Ultra", "description": "Compression maximale pour situations critiques", "boostFactor": 1.5, "stability": 0.9373882138964214, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518398247, "expiresAt": null}, "compression_fast_decompressor_1748518399279": {"name": "Décompresseur Rapide", "description": "Décompression ultra-rapide pour accès fréquents", "boostFactor": 1.5, "stability": 0.882142885225373, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518399279, "expiresAt": null}, "compression_thermal_compressor_1748518407548": {"name": "Compresseur <PERSON><PERSON>", "description": "Compression spécialisée pour mémoire thermique", "boostFactor": 1.5, "stability": 0.9207613074750631, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518407548, "expiresAt": null}, "compression_predictive_compressor_1748518408621": {"name": "Compresseur <PERSON><PERSON><PERSON>", "description": "Compression intelligente basée sur les patterns d'usage", "boostFactor": 1.5, "stability": 0.8644309572327502, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748518408621, "expiresAt": null}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.975, "stability": 0.8646247071805555, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748518409321, "expiresAt": 1748519009321}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.735, "stability": 0.9125028344394805, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748518409321, "expiresAt": 1748519009321}, "memory_compression_engine_1748518409322": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.4, "stability": 0.9014468397403291, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "memory_cache_optimizer_1748518409322": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.64, "stability": 0.8960911451969946, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "memory_garbage_collector_1748518409322": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.45, "stability": 0.9071515673866422, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7850000000000001, "stability": 0.8631196578831641, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748518410119, "expiresAt": 1748519010119}}, "totalAccelerators": 15, "activeAccelerators": 15, "averageBoostFactor": 2.1035666666666666, "efficiency": 0.826214, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748518462917, "totalBoostPower": 31.5535, "averageEnergy": 1, "averageStability": 0.9028034070014493, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748518462917, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0.3564208984375, "responseTime": 369.5951124661234, "overall": 0}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}