{"timestamp": "2025-05-29T12:15:06.489Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.96, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2477361324028853, "accessFactor": 1.05, "decayFactor": 0.9844163876060396, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10049999999999999, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.40529399937091964, "curiosity": 1, "confidence": -0.220860257732373, "energy": 1, "focus": 0.8817858733716535, "creativity": 1, "stress": 0.3755051627699121, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748520881111, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03039790713149, "neuronActivity": 0.5283460607999627}, "emotionalHistory": [{"timestamp": 1748520824347, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5198953565746106}, {"timestamp": 1748520849663, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01519895356574, "neuronActivity": 0.5198953565746106}, {"timestamp": 1748520881111, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03039790713149, "neuronActivity": 0.5283460607999627}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "8:13:44 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "8:14:09 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "8:14:41 AM", "mood": "curious"}], "circadianRhythm": 0.46798381890179286, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.9158771212162334, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.7952575583341355, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.943720777404, "energy": 1, "enabled": true, "type": "connection"}, "compression_basic_compressor_1748520738566": {"name": "Compresseur <PERSON>", "description": "Compression rapide pour données courantes", "boostFactor": 1.5, "stability": 0.821422641101677, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738566, "expiresAt": null}, "compression_advanced_compressor_1748520738591": {"name": "Compresseur <PERSON>", "description": "Compression haute efficacité avec algorithmes optimisés", "boostFactor": 1.5, "stability": 0.8697708427387485, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738591, "expiresAt": null}, "compression_ultra_compressor_1748520738596": {"name": "Compresseur Ultra", "description": "Compression maximale pour situations critiques", "boostFactor": 1.5, "stability": 0.8909723677447476, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738596, "expiresAt": null}, "compression_fast_decompressor_1748520743511": {"name": "Décompresseur Rapide", "description": "Décompression ultra-rapide pour accès fréquents", "boostFactor": 1.5, "stability": 0.8601157061768561, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520743511, "expiresAt": null}, "compression_thermal_compressor_1748520776692": {"name": "Compresseur <PERSON><PERSON>", "description": "Compression spécialisée pour mémoire thermique", "boostFactor": 1.5, "stability": 0.8530567621989344, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520776693, "expiresAt": null}, "compression_predictive_compressor_1748520799536": {"name": "Compresseur <PERSON><PERSON><PERSON>", "description": "Compression intelligente basée sur les patterns d'usage", "boostFactor": 1.5, "stability": 0.9760107539339868, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520799536, "expiresAt": null}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.8287794193608615, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520803937, "expiresAt": 1748521403937}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.9038102019026043, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748520803938, "expiresAt": 1748521403938}, "memory_compression_engine_1748520803938": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.3049999999999997, "stability": 0.8943406264560051, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "memory_cache_optimizer_1748520803938": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.583, "stability": 0.8665420381741391, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "memory_garbage_collector_1748520803938": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.4025000000000003, "stability": 0.8657309085874577, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.8290434819061072, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748520818964, "expiresAt": 1748521418964}, "emergency_memory_optimizer_4": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8860414115242804, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520844720, "expiresAt": 1748521144720}, "emergency_memory_optimizer_5": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8593711369983115, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520859554, "expiresAt": 1748521159554}, "emergency_memory_optimizer_6": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.9211938705102171, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520870866, "expiresAt": 1748521170866}, "emergency_memory_optimizer_7": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.92032588762077, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520886010, "expiresAt": 1748521186010}, "emergency_memory_optimizer_8": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8667525295011832, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520901362, "expiresAt": 1748521201362}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.8718, "stability": 0.9158771212162334, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.5288500000000003, "stability": 0.7952575583341355, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.014425, "stability": 0.943720777404, "energy": 1, "enabled": true, "type": "connection"}, "compression_basic_compressor_1748520738566": {"name": "Compresseur <PERSON>", "description": "Compression rapide pour données courantes", "boostFactor": 1.5, "stability": 0.821422641101677, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738566, "expiresAt": null}, "compression_advanced_compressor_1748520738591": {"name": "Compresseur <PERSON>", "description": "Compression haute efficacité avec algorithmes optimisés", "boostFactor": 1.5, "stability": 0.8697708427387485, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738591, "expiresAt": null}, "compression_ultra_compressor_1748520738596": {"name": "Compresseur Ultra", "description": "Compression maximale pour situations critiques", "boostFactor": 1.5, "stability": 0.8909723677447476, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738596, "expiresAt": null}, "compression_fast_decompressor_1748520743511": {"name": "Décompresseur Rapide", "description": "Décompression ultra-rapide pour accès fréquents", "boostFactor": 1.5, "stability": 0.8601157061768561, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520743511, "expiresAt": null}, "compression_thermal_compressor_1748520776692": {"name": "Compresseur <PERSON><PERSON>", "description": "Compression spécialisée pour mémoire thermique", "boostFactor": 1.5, "stability": 0.8530567621989344, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520776693, "expiresAt": null}, "compression_predictive_compressor_1748520799536": {"name": "Compresseur <PERSON><PERSON><PERSON>", "description": "Compression intelligente basée sur les patterns d'usage", "boostFactor": 1.5, "stability": 0.9760107539339868, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520799536, "expiresAt": null}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9512500000000002, "stability": 0.8287794193608615, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520803937, "expiresAt": 1748521403937}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.67325, "stability": 0.9038102019026043, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748520803938, "expiresAt": 1748521403938}, "memory_compression_engine_1748520803938": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.3049999999999997, "stability": 0.8943406264560051, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "memory_cache_optimizer_1748520803938": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.583, "stability": 0.8665420381741391, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "memory_garbage_collector_1748520803938": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.4025000000000003, "stability": 0.8657309085874577, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.77075, "stability": 0.8290434819061072, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748520818964, "expiresAt": 1748521418964}, "emergency_memory_optimizer_4": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8860414115242804, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520844720, "expiresAt": 1748521144720}, "emergency_memory_optimizer_5": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8593711369983115, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520859554, "expiresAt": 1748521159554}, "emergency_memory_optimizer_6": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.9211938705102171, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520870866, "expiresAt": 1748521170866}, "emergency_memory_optimizer_7": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.92032588762077, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520886010, "expiresAt": 1748521186010}, "emergency_memory_optimizer_8": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8667525295011832, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520901362, "expiresAt": 1748521201362}}, "totalAccelerators": 20, "activeAccelerators": 20, "averageBoostFactor": 2.52379125, "efficiency": 0.8514274749999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748520906488, "totalBoostPower": 50.475825, "averageEnergy": 1, "averageStability": 0.8784068021695628, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748520829623, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0, "responseTime": 359.2567106823206, "overall": 0}, {"timestamp": 1748520906488, "memoryEfficiency": 0, "thermalStability": 1, "cpuUsage": 0.3973388671875, "responseTime": 611.6059117982211, "overall": 0.579271583730671}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}