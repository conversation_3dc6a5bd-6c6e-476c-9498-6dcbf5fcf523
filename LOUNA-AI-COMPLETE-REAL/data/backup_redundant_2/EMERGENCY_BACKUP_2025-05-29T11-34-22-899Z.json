{"timestamp": "2025-05-29T11:34:22.899Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.95, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.3890289346001114, "curiosity": 1, "confidence": -0.22086474752865254, "energy": 1, "focus": 0.884291969008774, "creativity": 1, "stress": 0.4061486392101624, "fatigue": 0.003139433173077605, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748518452289, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01531721040176, "neuronActivity": 0.5317210401760929}, "emotionalHistory": [{"timestamp": 1748518415418, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5317210401760929}, {"timestamp": 1748518437288, "mood": "curious", "dominantEmotion": "energy", "qi": 120.01531721040176, "neuronActivity": 0.5317210401760929}, {"timestamp": 1748518452289, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01531721040176, "neuronActivity": 0.5317210401760929}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:33:35 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "7:33:57 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "7:34:12 AM", "mood": "curious"}], "circadianRhythm": 0.5561576160468032, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.8883242366186975, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.8544321316431841, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9582705222370833, "energy": 1, "enabled": true, "type": "connection"}, "compression_basic_compressor_1748518398221": {"name": "Compresseur <PERSON>", "description": "Compression rapide pour données courantes", "boostFactor": 1.5, "stability": 0.9, "energy": 800, "enabled": true, "type": "compression", "createdAt": 1748518398221, "expiresAt": null}, "compression_advanced_compressor_1748518398243": {"name": "Compresseur <PERSON>", "description": "Compression haute efficacité avec algorithmes optimisés", "boostFactor": 1.5, "stability": 0.9, "energy": 1200, "enabled": true, "type": "compression", "createdAt": 1748518398243, "expiresAt": null}, "compression_ultra_compressor_1748518398247": {"name": "Compresseur Ultra", "description": "Compression maximale pour situations critiques", "boostFactor": 1.5, "stability": 0.9, "energy": 1800, "enabled": true, "type": "compression", "createdAt": 1748518398247, "expiresAt": null}, "compression_fast_decompressor_1748518399279": {"name": "Décompresseur Rapide", "description": "Décompression ultra-rapide pour accès fréquents", "boostFactor": 1.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "compression", "createdAt": 1748518399279, "expiresAt": null}, "compression_thermal_compressor_1748518407548": {"name": "Compresseur <PERSON><PERSON>", "description": "Compression spécialisée pour mémoire thermique", "boostFactor": 1.5, "stability": 0.9, "energy": 1500, "enabled": true, "type": "compression", "createdAt": 1748518407548, "expiresAt": null}, "compression_predictive_compressor_1748518408621": {"name": "Compresseur <PERSON><PERSON><PERSON>", "description": "Compression intelligente basée sur les patterns d'usage", "boostFactor": 1.5, "stability": 0.9, "energy": 1400, "enabled": true, "type": "compression", "createdAt": 1748518408621, "expiresAt": null}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748518409321, "expiresAt": 1748519009321}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748518409321, "expiresAt": 1748519009321}, "memory_compression_engine_1748518409322": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "memory_cache_optimizer_1748518409322": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "memory_garbage_collector_1748518409322": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748518410119, "expiresAt": 1748519010119}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.8883242366186975, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.8544321316431841, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9582705222370833, "energy": 1, "enabled": true, "type": "connection"}, "compression_basic_compressor_1748518398221": {"name": "Compresseur <PERSON>", "description": "Compression rapide pour données courantes", "boostFactor": 1.5, "stability": 0.9, "energy": 800, "enabled": true, "type": "compression", "createdAt": 1748518398221, "expiresAt": null}, "compression_advanced_compressor_1748518398243": {"name": "Compresseur <PERSON>", "description": "Compression haute efficacité avec algorithmes optimisés", "boostFactor": 1.5, "stability": 0.9, "energy": 1200, "enabled": true, "type": "compression", "createdAt": 1748518398243, "expiresAt": null}, "compression_ultra_compressor_1748518398247": {"name": "Compresseur Ultra", "description": "Compression maximale pour situations critiques", "boostFactor": 1.5, "stability": 0.9, "energy": 1800, "enabled": true, "type": "compression", "createdAt": 1748518398247, "expiresAt": null}, "compression_fast_decompressor_1748518399279": {"name": "Décompresseur Rapide", "description": "Décompression ultra-rapide pour accès fréquents", "boostFactor": 1.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "compression", "createdAt": 1748518399279, "expiresAt": null}, "compression_thermal_compressor_1748518407548": {"name": "Compresseur <PERSON><PERSON>", "description": "Compression spécialisée pour mémoire thermique", "boostFactor": 1.5, "stability": 0.9, "energy": 1500, "enabled": true, "type": "compression", "createdAt": 1748518407548, "expiresAt": null}, "compression_predictive_compressor_1748518408621": {"name": "Compresseur <PERSON><PERSON><PERSON>", "description": "Compression intelligente basée sur les patterns d'usage", "boostFactor": 1.5, "stability": 0.9, "energy": 1400, "enabled": true, "type": "compression", "createdAt": 1748518408621, "expiresAt": null}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748518409321, "expiresAt": 1748519009321}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748518409321, "expiresAt": 1748519009321}, "memory_compression_engine_1748518409322": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "memory_cache_optimizer_1748518409322": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "memory_garbage_collector_1748518409322": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748518409322, "expiresAt": 1748519309322}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748518410119, "expiresAt": 1748519010119}}, "totalAccelerators": 15, "activeAccelerators": 15, "averageBoostFactor": 2.5766666666666667, "efficiency": 0.8545999999999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748518398168, "totalBoostPower": 32.03, "averageEnergy": 913.5333333333333, "averageStability": 0.9000684593665979, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}