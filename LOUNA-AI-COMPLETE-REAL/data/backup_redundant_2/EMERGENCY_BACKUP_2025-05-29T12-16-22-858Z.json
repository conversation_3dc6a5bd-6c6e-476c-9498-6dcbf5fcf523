{"timestamp": "2025-05-29T12:16:22.858Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.97, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2621540411577572, "accessFactor": 1.05, "decayFactor": 0.9886023245510082, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.10100249999999998, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0, "curiosity": 1, "confidence": -0.9700106193418667, "energy": 1, "focus": 1, "creativity": 1, "stress": 0.7291673627096812, "fatigue": 0, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748520967919, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0610032275111, "neuronActivity": 0.5321859771598884}, "emotionalHistory": [{"timestamp": 1748520824347, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5198953565746106}, {"timestamp": 1748520849663, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01519895356574, "neuronActivity": 0.5198953565746106}, {"timestamp": 1748520881111, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03039790713149, "neuronActivity": 0.5283460607999627}, {"timestamp": 1748520911461, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.03039790713149, "neuronActivity": 0.5283460607999627}, {"timestamp": 1748520937683, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.04568136773949, "neuronActivity": 0.5321859771598884}, {"timestamp": 1748520967919, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.0610032275111, "neuronActivity": 0.5321859771598884}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "8:14:09 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "8:14:41 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "8:15:11 AM", "mood": "curious"}, {"text": "Chaque nouvelle information enrichit ma compréhension du monde.", "time": "8:15:37 AM", "mood": "curious"}, {"text": "Je me demande quelles nouvelles connaissances je vais découvrir...", "time": "8:16:07 AM", "mood": "curious"}], "circadianRhythm": 0.4648345636474289, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.80321, "stability": 0.8730761099838155, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4774075000000004, "stability": 0.7752070201039936, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9887037500000002, "stability": 0.8990707783602128, "energy": 1, "enabled": true, "type": "connection"}, "compression_basic_compressor_1748520738566": {"name": "Compresseur <PERSON>", "description": "Compression rapide pour données courantes", "boostFactor": 1.5, "stability": 0.7884813599631112, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738566, "expiresAt": null}, "compression_advanced_compressor_1748520738591": {"name": "Compresseur <PERSON>", "description": "Compression haute efficacité avec algorithmes optimisés", "boostFactor": 1.5, "stability": 0.8718848050369177, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738591, "expiresAt": null}, "compression_ultra_compressor_1748520738596": {"name": "Compresseur Ultra", "description": "Compression maximale pour situations critiques", "boostFactor": 1.5, "stability": 0.9277623291733271, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738596, "expiresAt": null}, "compression_fast_decompressor_1748520743511": {"name": "Décompresseur Rapide", "description": "Décompression ultra-rapide pour accès fréquents", "boostFactor": 1.5, "stability": 0.8565211599367734, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520743511, "expiresAt": null}, "compression_thermal_compressor_1748520776692": {"name": "Compresseur <PERSON><PERSON>", "description": "Compression spécialisée pour mémoire thermique", "boostFactor": 1.5, "stability": 0.8449983801636894, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520776693, "expiresAt": null}, "compression_predictive_compressor_1748520799536": {"name": "Compresseur <PERSON><PERSON><PERSON>", "description": "Compression intelligente basée sur les patterns d'usage", "boostFactor": 1.5, "stability": 0.9330024587975085, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520799536, "expiresAt": null}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9286875, "stability": 0.8266121826012828, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520803937, "expiresAt": 1748521403937}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.6145875, "stability": 0.8841247207962026, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748520803938, "expiresAt": 1748521403938}, "memory_compression_engine_1748520803938": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.2147499999999996, "stability": 0.917379688771552, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "memory_cache_optimizer_1748520803938": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.5288500000000003, "stability": 0.8518394708981216, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "memory_garbage_collector_1748520803938": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.357375, "stability": 0.8521461184983354, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7572125, "stability": 0.83511941793086, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748520818964, "expiresAt": 1748521418964}, "emergency_memory_optimizer_4": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.8926181057364064, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520844720, "expiresAt": 1748521144720}, "emergency_memory_optimizer_5": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.881752657241922, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520859554, "expiresAt": 1748521159554}, "emergency_memory_optimizer_6": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.9521768012699078, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520870866, "expiresAt": 1748521170866}, "emergency_memory_optimizer_7": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.9511116923445969, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520886010, "expiresAt": 1748521186010}, "emergency_memory_optimizer_8": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.8321279254146453, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520901362, "expiresAt": 1748521201362}, "emergency_memory_optimizer_9": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.9348147316644493, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520916416, "expiresAt": 1748521216416}, "emergency_memory_optimizer_10": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.9274166738311754, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520932178, "expiresAt": 1748521232178}, "emergency_memory_optimizer_11": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8971956253747971, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520947577, "expiresAt": 1748521247577}, "emergency_memory_optimizer_12": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.889059267630189, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520962982, "expiresAt": 1748521262982}, "emergency_memory_optimizer_13": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.858998026482735, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520977832, "expiresAt": 1748521277832}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 2.80321, "stability": 0.8730761099838155, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.4774075000000004, "stability": 0.7752070201039936, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 1.9887037500000002, "stability": 0.8990707783602128, "energy": 1, "enabled": true, "type": "connection"}, "compression_basic_compressor_1748520738566": {"name": "Compresseur <PERSON>", "description": "Compression rapide pour données courantes", "boostFactor": 1.5, "stability": 0.7884813599631112, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738566, "expiresAt": null}, "compression_advanced_compressor_1748520738591": {"name": "Compresseur <PERSON>", "description": "Compression haute efficacité avec algorithmes optimisés", "boostFactor": 1.5, "stability": 0.8718848050369177, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738591, "expiresAt": null}, "compression_ultra_compressor_1748520738596": {"name": "Compresseur Ultra", "description": "Compression maximale pour situations critiques", "boostFactor": 1.5, "stability": 0.9277623291733271, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520738596, "expiresAt": null}, "compression_fast_decompressor_1748520743511": {"name": "Décompresseur Rapide", "description": "Décompression ultra-rapide pour accès fréquents", "boostFactor": 1.5, "stability": 0.8565211599367734, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520743511, "expiresAt": null}, "compression_thermal_compressor_1748520776692": {"name": "Compresseur <PERSON><PERSON>", "description": "Compression spécialisée pour mémoire thermique", "boostFactor": 1.5, "stability": 0.8449983801636894, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520776693, "expiresAt": null}, "compression_predictive_compressor_1748520799536": {"name": "Compresseur <PERSON><PERSON><PERSON>", "description": "Compression intelligente basée sur les patterns d'usage", "boostFactor": 1.5, "stability": 0.9330024587975085, "energy": 1, "enabled": true, "type": "compression", "createdAt": 1748520799536, "expiresAt": null}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 1.9286875, "stability": 0.8266121826012828, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520803937, "expiresAt": 1748521403937}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.6145875, "stability": 0.8841247207962026, "energy": 1, "enabled": true, "type": "qi_enhancer", "createdAt": 1748520803938, "expiresAt": 1748521403938}, "memory_compression_engine_1748520803938": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.2147499999999996, "stability": 0.917379688771552, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "memory_cache_optimizer_1748520803938": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.5288500000000003, "stability": 0.8518394708981216, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "memory_garbage_collector_1748520803938": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.357375, "stability": 0.8521461184983354, "energy": 1, "enabled": true, "type": "processing", "createdAt": 1748520803938, "expiresAt": 1748521703938}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.7572125, "stability": 0.83511941793086, "energy": 1, "enabled": true, "type": "thermal_cooler", "createdAt": 1748520818964, "expiresAt": 1748521418964}, "emergency_memory_optimizer_4": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.8926181057364064, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520844720, "expiresAt": 1748521144720}, "emergency_memory_optimizer_5": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.881752657241922, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520859554, "expiresAt": 1748521159554}, "emergency_memory_optimizer_6": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.9521768012699078, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520870866, "expiresAt": 1748521170866}, "emergency_memory_optimizer_7": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.9511116923445969, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520886010, "expiresAt": 1748521186010}, "emergency_memory_optimizer_8": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.75625, "stability": 0.8321279254146453, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520901362, "expiresAt": 1748521201362}, "emergency_memory_optimizer_9": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.9348147316644493, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520916416, "expiresAt": 1748521216416}, "emergency_memory_optimizer_10": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.9274166738311754, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520932178, "expiresAt": 1748521232178}, "emergency_memory_optimizer_11": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.8971956253747971, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520947577, "expiresAt": 1748521247577}, "emergency_memory_optimizer_12": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.889059267630189, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520962982, "expiresAt": 1748521262982}, "emergency_memory_optimizer_13": {"name": "URGENCE Memory Optimizer", "description": "Optimise l'usage mémoire - MODE URGENCE", "boostFactor": 3.875, "stability": 0.858998026482735, "energy": 1, "enabled": true, "type": "memory_optimizer", "createdAt": 1748520977832, "expiresAt": 1748521277832}}, "totalAccelerators": 25, "activeAccelerators": 25, "averageBoostFactor": 2.75308135, "efficiency": 0.865184881, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748520982829, "totalBoostPower": 68.82703375, "averageEnergy": 1, "averageStability": 0.878179900320261, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [{"timestamp": 1748520829623, "memoryEfficiency": 0, "thermalStability": 0, "cpuUsage": 0, "responseTime": 359.2567106823206, "overall": 0}, {"timestamp": 1748520906488, "memoryEfficiency": 0, "thermalStability": 1, "cpuUsage": 0.3973388671875, "responseTime": 611.6059117982211, "overall": 0.579271583730671}, {"timestamp": 1748520982829, "memoryEfficiency": 0, "thermalStability": 0.9506517965346575, "cpuUsage": 0.4287109375, "responseTime": 370.0714371816977, "overall": 0.5584627046871088}], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}