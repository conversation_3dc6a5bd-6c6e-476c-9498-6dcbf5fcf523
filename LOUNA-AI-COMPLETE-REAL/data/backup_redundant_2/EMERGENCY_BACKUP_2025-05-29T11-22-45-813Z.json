{"timestamp": "2025-05-29T11:22:45.813Z", "backupType": "complete", "systems": {"thermalMemory": {"zones": {}, "emotionalState": {}, "stats": {}, "config": {"memoryCycleInterval": 300, "memoryDecayRate": 0.95, "instantCapacity": 20, "shortTermCapacity": 50, "workingMemoryCapacity": 100, "mediumTermCapacity": 200, "longTermCapacity": 1000, "dreamMemoryCapacity": 50, "importanceFactor": 1.2, "accessFactor": 1.05, "decayFactor": 0.98, "hotThreshold": 0.8, "warmThreshold": 0.6, "coolThreshold": 0.4, "coldThreshold": 0.2, "temperatureCursorEnabled": true, "temperatureCursorSensitivity": 0.1, "temperatureCursorMin": 0.3, "temperatureCursorMax": 0.7}}, "artificialBrain": {"neurons": [], "synapses": [], "networks": {}, "qi": 0, "emotionalState": {"happiness": 0.5181812004182978, "curiosity": 1, "confidence": 0.044464546651870274, "energy": 1, "focus": 0.8095148054818271, "creativity": 1, "stress": 0.283308545334813, "fatigue": 0.07217569710528565, "mood": "curious", "emotionalStability": 0.8, "personality": {"openness": 0.9, "conscientiousness": 0.8, "extraversion": 0.6, "agreeableness": 0.8, "neuroticism": 0.3}, "lastEmotionalEvent": {"timestamp": 1748517746383, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01515550623432, "neuronActivity": 0.5155506234323002}, "emotionalHistory": [{"timestamp": 1748517722172, "mood": "curious", "dominantEmotion": "curiosity", "qi": 120, "neuronActivity": 0.5155506234323002}, {"timestamp": 1748517746383, "mood": "curious", "dominantEmotion": "creativity", "qi": 120.01515550623432, "neuronActivity": 0.5155506234323002}], "currentThoughts": [{"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:22:02 AM", "mood": "curious"}, {"text": "Il y a tant de choses fascinantes à explorer !", "time": "7:22:26 AM", "mood": "curious"}], "circadianRhythm": 0.5815774950695496, "mentalCycles": {"attention": 0.7, "memory": 0.8, "processing": 0.75}}, "learningHistory": []}, "kyberAccelerators": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9191484449313719, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.9140299353591023, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9435492556950276, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748517715358, "expiresAt": 1748518315358}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748517715358, "expiresAt": 1748518315358}, "memory_compression_engine_1748517715359": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "memory_cache_optimizer_1748517715359": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "memory_garbage_collector_1748517715359": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748517716276, "expiresAt": 1748518316276}}, "stats": {"accelerators": {"reflexive": {"name": "Accélérateur Réflexif", "description": "Améliore la vitesse de traitement des informations", "boostFactor": 3.02, "stability": 0.9191484449313719, "energy": 1, "enabled": true, "type": "processing"}, "thermal": {"name": "Accélérateur Thermique", "description": "Optimise les transferts entre zones de mémoire thermique", "boostFactor": 2.64, "stability": 0.9140299353591023, "energy": 1, "enabled": true, "type": "memory"}, "connector": {"name": "Connecteur Thermique", "description": "Facilite les connexions entre informations dans différentes zones", "boostFactor": 2.0700000000000003, "stability": 0.9435492556950276, "energy": 1, "enabled": true, "type": "connection"}, "memory_optimizer_auto_1": {"name": "Memory Optimizer", "description": "Optimise l'usage mémoire", "boostFactor": 2, "stability": 0.9, "energy": 1000, "enabled": true, "type": "memory_optimizer", "createdAt": 1748517715358, "expiresAt": 1748518315358}, "qi_enhancer_auto_2": {"name": "QI Enhancer", "description": "Am<PERSON><PERSON>re le niveau de QI", "boostFactor": 2.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "qi_enhancer", "createdAt": 1748517715358, "expiresAt": 1748518315358}, "memory_compression_engine_1748517715359": {"name": "Moteur de Compression Avancé", "description": "Compression de données ultra-efficace", "boostFactor": 3.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "memory_cache_optimizer_1748517715359": {"name": "Optimiseur de Cache Intelligent", "description": "Gestion prédictive du cache", "boostFactor": 2.7, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "memory_garbage_collector_1748517715359": {"name": "Collecteur <PERSON> Déchets <PERSON>nc<PERSON>", "description": "Nettoyage mémoire intelligent et prédictif", "boostFactor": 2.5, "stability": 0.9, "energy": 1000, "enabled": true, "type": "processing", "createdAt": 1748517715359, "expiresAt": 1748518615359}, "thermal_cooler_auto_3": {"name": "Thermal Cooler", "description": "Refroidit la mémoire thermique", "boostFactor": 1.8, "stability": 0.9, "energy": 1000, "enabled": true, "type": "thermal_cooler", "createdAt": 1748517716276, "expiresAt": 1748518316276}}, "totalAccelerators": 9, "activeAccelerators": 9, "averageBoostFactor": 2.5766666666666667, "efficiency": 0.8545999999999999, "totalBoostApplied": 0, "energyConsumed": 0, "stabilityEvents": 0, "lastUpdateTime": 1748517705555, "totalBoostPower": 23.03, "averageEnergy": 667, "averageStability": 0.908525292887278, "totalOptimizations": 0}, "config": {"updateInterval": 60, "maxBoostFactor": 5, "minBoostFactor": 1, "defaultBoostFactor": 1.5, "stabilityThreshold": 0.7, "adaptationRate": 0.05, "energyConsumptionRate": 0.01, "energyRegenerationRate": 0.005}}, "autoIntelligence": {"performanceHistory": [], "autoAccelerators": {}, "config": {"memoryCheckInterval": 30000, "acceleratorCheckInterval": 15000, "compressionThreshold": 1048576, "performanceThreshold": 0.7, "autoOptimizationEnabled": true, "intelligenceTransferEnabled": true}}}}