const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const crypto = require('crypto');

/**
 * 🧠 SYSTÈME DE MÉMOIRE THERMIQUE RÉELLE
 * Remplace les simulations par de vraies fonctionnalités basées sur les neurosciences
 */
class RealThermalMemorySystem extends EventEmitter {
    constructor() {
        super();
        
        // 🧠 ZONES MÉMOIRE BASÉES SUR LA NEUROSCIENCE RÉELLE
        this.memoryZones = {
            // Zone 1: Registres sensoriels (0-1 seconde)
            sensory: {
                capacity: 1000,
                retention: 1000, // 1 seconde
                entries: new Map(),
                temperature: 37.0,
                activity: 0.9
            },
            // Zone 2: Mémoire de travail (1-30 secondes)
            working: {
                capacity: 100,
                retention: 30000, // 30 secondes
                entries: new Map(),
                temperature: 37.2,
                activity: 0.8
            },
            // Zone 3: Mémoire à court terme (30s-30min)
            shortTerm: {
                capacity: 500,
                retention: 1800000, // 30 minutes
                entries: new Map(),
                temperature: 37.1,
                activity: 0.6
            },
            // Zone 4: Mémoire épisodique (30min-24h)
            episodic: {
                capacity: 2000,
                retention: 86400000, // 24 heures
                entries: new Map(),
                temperature: 36.9,
                activity: 0.4
            },
            // Zone 5: Mémoire sémantique (1 jour+)
            semantic: {
                capacity: 10000,
                retention: -1, // Permanent
                entries: new Map(),
                temperature: 36.8,
                activity: 0.3
            },
            // Zone 6: Mémoire procédurale (permanent)
            procedural: {
                capacity: 5000,
                retention: -1, // Permanent
                entries: new Map(),
                temperature: 36.7,
                activity: 0.2
            }
        };

        // 🧠 MÉCANISMES NEUROSCIENTIFIQUES RÉELS
        this.neuralMechanisms = {
            // Potentialisation à long terme (LTP)
            ltp: {
                threshold: 0.7,
                strengthening: 1.5,
                duration: 3600000 // 1 heure
            },
            // Dépression à long terme (LTD)
            ltd: {
                threshold: 0.3,
                weakening: 0.5,
                duration: 1800000 // 30 minutes
            },
            // Consolidation mémoire
            consolidation: {
                hippocampalToCorticaRate: 0.1,
                sleepBonus: 2.0,
                stressReduction: 0.8
            },
            // Neurogenèse (700 nouveaux neurones/jour dans l'hippocampe)
            neurogenesis: {
                rate: 700 / 86400000, // par milliseconde
                lastGeneration: Date.now(),
                totalGenerated: 0
            }
        };

        // 📊 MÉTRIQUES SYSTÈME RÉELLES
        this.systemMetrics = {
            cpuUsage: 0,
            memoryUsage: 0,
            diskIO: 0,
            networkActivity: 0,
            processCount: 0,
            temperature: 37.0
        };

        // 🔄 ÉTAT DU SYSTÈME
        this.state = {
            isActive: false,
            totalEntries: 0,
            totalConsolidations: 0,
            totalNeurogenesis: 0,
            efficiency: 1.0,
            lastCleanup: Date.now()
        };

        this.initialize();
    }

    /**
     * 🚀 Initialise le système de mémoire thermique réelle
     */
    async initialize() {
        console.log('🧠 Initialisation système mémoire thermique réelle...');
        
        try {
            // Charger les données existantes
            await this.loadMemoryData();
            
            // Démarrer les processus automatiques
            this.startAutomaticProcesses();
            
            // Démarrer la surveillance système
            this.startSystemMonitoring();
            
            this.state.isActive = true;
            
            console.log('🧠 Système mémoire thermique réelle initialisé');
            this.emit('initialized');
            
        } catch (error) {
            console.error('❌ Erreur initialisation mémoire thermique:', error);
            throw error;
        }
    }

    /**
     * 📊 Démarre la surveillance des métriques système
     */
    startSystemMonitoring() {
        setInterval(() => {
            this.updateSystemMetrics();
            this.adjustTemperatureBasedOnSystem();
        }, 2000);
    }

    /**
     * 📈 Met à jour les métriques système réelles
     */
    updateSystemMetrics() {
        const cpus = os.cpus();
        const memUsage = process.memoryUsage();
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        
        // Calculer l'usage CPU réel
        this.systemMetrics.cpuUsage = this.calculateCPUUsage();
        
        // Calculer l'usage mémoire réel
        this.systemMetrics.memoryUsage = ((totalMem - freeMem) / totalMem) * 100;
        
        // Calculer la température basée sur l'activité
        this.systemMetrics.temperature = this.calculateSystemTemperature();
        
        // Compter les processus actifs
        this.systemMetrics.processCount = Object.keys(this.memoryZones).reduce(
            (total, zone) => total + this.memoryZones[zone].entries.size, 0
        );
    }

    /**
     * 🌡️ Calcule la température système basée sur l'activité réelle
     */
    calculateSystemTemperature() {
        const baseTemp = 36.5; // Température corporelle de base
        const cpuHeat = (this.systemMetrics.cpuUsage / 100) * 2.0;
        const memoryHeat = (this.systemMetrics.memoryUsage / 100) * 1.5;
        const activityHeat = (this.state.totalEntries / 10000) * 1.0;
        
        return baseTemp + cpuHeat + memoryHeat + activityHeat;
    }

    /**
     * 📊 Calcule l'usage CPU réel
     */
    calculateCPUUsage() {
        const cpus = os.cpus();
        
        if (!this.previousCPUInfo) {
            this.previousCPUInfo = cpus;
            return 0;
        }
        
        let totalIdle = 0;
        let totalTick = 0;
        let totalIdlePrev = 0;
        let totalTickPrev = 0;
        
        for (let i = 0; i < cpus.length; i++) {
            const cpu = cpus[i];
            const cpuPrev = this.previousCPUInfo[i];
            
            if (cpuPrev) {
                for (let type in cpu.times) {
                    totalTick += cpu.times[type];
                    totalTickPrev += cpuPrev.times[type];
                }
                
                totalIdle += cpu.times.idle;
                totalIdlePrev += cpuPrev.times.idle;
            }
        }
        
        const totalDiff = totalTick - totalTickPrev;
        const idleDiff = totalIdle - totalIdlePrev;
        
        this.previousCPUInfo = cpus;
        
        return totalDiff > 0 ? 100 - ~~(100 * idleDiff / totalDiff) : 0;
    }

    /**
     * 🔄 Ajuste la température des zones basée sur le système
     */
    adjustTemperatureBasedOnSystem() {
        const systemTemp = this.systemMetrics.temperature;
        
        Object.keys(this.memoryZones).forEach(zoneName => {
            const zone = this.memoryZones[zoneName];
            const activityFactor = zone.activity;
            const loadFactor = zone.entries.size / zone.capacity;
            
            // Ajuster la température basée sur l'activité réelle
            zone.temperature = systemTemp + (activityFactor * loadFactor * 2.0);
        });
    }

    /**
     * 🔄 Démarre les processus automatiques
     */
    startAutomaticProcesses() {
        // Consolidation mémoire toutes les 30 secondes
        setInterval(() => {
            this.performMemoryConsolidation();
        }, 30000);
        
        // Neurogenèse toutes les minutes
        setInterval(() => {
            this.performNeurogenesis();
        }, 60000);
        
        // Nettoyage automatique toutes les 5 minutes
        setInterval(() => {
            this.performAutomaticCleanup();
        }, 300000);
        
        // LTP/LTD toutes les 10 secondes
        setInterval(() => {
            this.performSynapticPlasticity();
        }, 10000);
    }

    /**
     * 💾 Ajoute une entrée à la mémoire thermique
     */
    addEntry(data, importance = 0.5, category = 'general') {
        const entryId = this.generateEntryId();
        const timestamp = Date.now();
        
        const entry = {
            id: entryId,
            data,
            importance,
            category,
            timestamp,
            accessCount: 0,
            lastAccessed: null,
            temperature: this.calculateEntryTemperature(importance),
            synapticStrength: importance,
            ltpLevel: 0,
            consolidationStatus: 'hippocampal',
            memoryZone: this.determineMemoryZone(importance, category),
            hash: this.generateDataHash(data)
        };
        
        // Stocker dans la zone appropriée
        const zone = this.memoryZones[entry.memoryZone];
        if (zone) {
            zone.entries.set(entryId, entry);
            this.state.totalEntries++;
            
            // Émettre événement
            this.emit('entryAdded', entry);
            
            console.log(`🧠 Entrée ajoutée en zone ${entry.memoryZone}: ${data.substring(0, 50)}...`);
        }
        
        return entry;
    }

    /**
     * 🔍 Récupère une entrée par ID
     */
    getEntry(entryId) {
        for (const zoneName in this.memoryZones) {
            const zone = this.memoryZones[zoneName];
            if (zone.entries.has(entryId)) {
                const entry = zone.entries.get(entryId);
                
                // Mettre à jour les statistiques d'accès
                entry.accessCount++;
                entry.lastAccessed = Date.now();
                
                // Renforcer la synapse (LTP)
                if (entry.synapticStrength < 1.0) {
                    entry.synapticStrength += 0.1;
                }
                
                this.emit('entryAccessed', entry);
                return entry;
            }
        }
        return null;
    }

    /**
     * 🧠 Détermine la zone mémoire appropriée
     */
    determineMemoryZone(importance, category) {
        if (importance > 0.9) return 'semantic';
        if (importance > 0.7) return 'episodic';
        if (importance > 0.5) return 'shortTerm';
        if (importance > 0.3) return 'working';
        return 'sensory';
    }

    /**
     * 🌡️ Calcule la température d'une entrée
     */
    calculateEntryTemperature(importance) {
        const baseTemp = 36.5;
        const importanceHeat = importance * 2.0;
        const systemHeat = (this.systemMetrics.cpuUsage / 100) * 1.0;
        
        return baseTemp + importanceHeat + systemHeat;
    }

    /**
     * 🔑 Génère un ID unique pour une entrée
     */
    generateEntryId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        const memUsage = process.memoryUsage().heapUsed.toString(36).substr(-4);
        
        return `thermal_${timestamp}_${random}_${memUsage}`;
    }

    /**
     * 🔐 Génère un hash des données
     */
    generateDataHash(data) {
        return crypto.createHash('sha256').update(JSON.stringify(data)).digest('hex').substr(0, 16);
    }

    /**
     * 🔄 Effectue la consolidation mémoire (hippocampe → cortex)
     */
    performMemoryConsolidation() {
        const workingZone = this.memoryZones.working;
        const episodicZone = this.memoryZones.episodic;
        const semanticZone = this.memoryZones.semantic;
        
        let consolidatedCount = 0;
        
        // Consolider de working vers episodic
        workingZone.entries.forEach((entry, id) => {
            if (entry.synapticStrength > 0.6 && Date.now() - entry.timestamp > 30000) {
                episodicZone.entries.set(id, entry);
                workingZone.entries.delete(id);
                entry.memoryZone = 'episodic';
                entry.consolidationStatus = 'cortical';
                consolidatedCount++;
            }
        });
        
        // Consolider de episodic vers semantic
        episodicZone.entries.forEach((entry, id) => {
            if (entry.synapticStrength > 0.8 && Date.now() - entry.timestamp > 3600000) {
                semanticZone.entries.set(id, entry);
                episodicZone.entries.delete(id);
                entry.memoryZone = 'semantic';
                entry.consolidationStatus = 'permanent';
                consolidatedCount++;
            }
        });
        
        if (consolidatedCount > 0) {
            this.state.totalConsolidations += consolidatedCount;
            console.log(`🔄 Consolidation mémoire: ${consolidatedCount} entrées consolidées`);
            this.emit('memoryConsolidated', { count: consolidatedCount });
        }
    }

    /**
     * 🧬 Effectue la neurogenèse (génération de nouveaux neurones)
     */
    performNeurogenesis() {
        const timeSinceLastGeneration = Date.now() - this.neuralMechanisms.neurogenesis.lastGeneration;
        const newNeurons = Math.floor(timeSinceLastGeneration * this.neuralMechanisms.neurogenesis.rate);
        
        if (newNeurons > 0) {
            this.neuralMechanisms.neurogenesis.totalGenerated += newNeurons;
            this.neuralMechanisms.neurogenesis.lastGeneration = Date.now();
            this.state.totalNeurogenesis += newNeurons;
            
            // Augmenter légèrement la capacité des zones
            Object.keys(this.memoryZones).forEach(zoneName => {
                this.memoryZones[zoneName].capacity += Math.floor(newNeurons / 6);
            });
            
            console.log(`🧬 Neurogenèse: ${newNeurons} nouveaux neurones générés`);
            this.emit('neurogenesis', { newNeurons });
        }
    }

    /**
     * ⚡ Effectue la plasticité synaptique (LTP/LTD)
     */
    performSynapticPlasticity() {
        let ltpCount = 0;
        let ltdCount = 0;
        
        Object.keys(this.memoryZones).forEach(zoneName => {
            const zone = this.memoryZones[zoneName];
            
            zone.entries.forEach((entry, id) => {
                // LTP: Renforcement des synapses fréquemment utilisées
                if (entry.accessCount > 5 && entry.synapticStrength < 1.0) {
                    entry.synapticStrength = Math.min(1.0, entry.synapticStrength + 0.05);
                    entry.ltpLevel++;
                    ltpCount++;
                }
                
                // LTD: Affaiblissement des synapses peu utilisées
                if (entry.accessCount === 0 && Date.now() - entry.timestamp > 3600000) {
                    entry.synapticStrength = Math.max(0.1, entry.synapticStrength - 0.02);
                    ltdCount++;
                }
            });
        });
        
        if (ltpCount > 0 || ltdCount > 0) {
            console.log(`⚡ Plasticité synaptique: LTP=${ltpCount}, LTD=${ltdCount}`);
            this.emit('synapticPlasticity', { ltp: ltpCount, ltd: ltdCount });
        }
    }

    /**
     * 🧹 Effectue le nettoyage automatique
     */
    performAutomaticCleanup() {
        let cleanedCount = 0;
        const now = Date.now();
        
        Object.keys(this.memoryZones).forEach(zoneName => {
            const zone = this.memoryZones[zoneName];
            
            if (zone.retention > 0) {
                zone.entries.forEach((entry, id) => {
                    if (now - entry.timestamp > zone.retention && entry.synapticStrength < 0.3) {
                        zone.entries.delete(id);
                        cleanedCount++;
                    }
                });
            }
        });
        
        if (cleanedCount > 0) {
            this.state.totalEntries -= cleanedCount;
            console.log(`🧹 Nettoyage automatique: ${cleanedCount} entrées supprimées`);
            this.emit('automaticCleanup', { cleaned: cleanedCount });
        }
        
        this.state.lastCleanup = now;
    }

    /**
     * 💾 Sauvegarde les données mémoire
     */
    async saveMemoryData() {
        try {
            const dataPath = path.join(__dirname, '../data/memory');
            await fs.mkdir(dataPath, { recursive: true });
            
            const memoryData = {
                zones: {},
                state: this.state,
                systemMetrics: this.systemMetrics,
                neuralMechanisms: this.neuralMechanisms,
                timestamp: Date.now()
            };
            
            // Convertir les Maps en objets pour la sérialisation
            Object.keys(this.memoryZones).forEach(zoneName => {
                const zone = this.memoryZones[zoneName];
                memoryData.zones[zoneName] = {
                    ...zone,
                    entries: Object.fromEntries(zone.entries)
                };
            });
            
            const filePath = path.join(dataPath, 'real_thermal_memory.json');
            await fs.writeFile(filePath, JSON.stringify(memoryData, null, 2));
            
            console.log('💾 Données mémoire thermique sauvegardées');
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde mémoire:', error);
        }
    }

    /**
     * 📂 Charge les données mémoire
     */
    async loadMemoryData() {
        try {
            const dataPath = path.join(__dirname, '../data/memory/real_thermal_memory.json');
            const data = await fs.readFile(dataPath, 'utf8');
            const memoryData = JSON.parse(data);
            
            // Restaurer les zones mémoire
            Object.keys(memoryData.zones).forEach(zoneName => {
                if (this.memoryZones[zoneName]) {
                    const zoneData = memoryData.zones[zoneName];
                    this.memoryZones[zoneName] = {
                        ...this.memoryZones[zoneName],
                        ...zoneData,
                        entries: new Map(Object.entries(zoneData.entries))
                    };
                }
            });
            
            // Restaurer l'état
            this.state = { ...this.state, ...memoryData.state };
            
            console.log('📂 Données mémoire thermique chargées');
            
        } catch (error) {
            console.log('📂 Aucune donnée mémoire existante, démarrage à neuf');
        }
    }

    /**
     * 📊 Obtient les statistiques du système
     */
    getStats() {
        const stats = {
            state: this.state,
            systemMetrics: this.systemMetrics,
            zones: {}
        };
        
        Object.keys(this.memoryZones).forEach(zoneName => {
            const zone = this.memoryZones[zoneName];
            stats.zones[zoneName] = {
                capacity: zone.capacity,
                used: zone.entries.size,
                temperature: zone.temperature,
                activity: zone.activity,
                retention: zone.retention
            };
        });
        
        return stats;
    }

    /**
     * 🛑 Arrête le système
     */
    async shutdown() {
        console.log('🛑 Arrêt système mémoire thermique...');
        
        // Sauvegarder avant l'arrêt
        await this.saveMemoryData();
        
        this.state.isActive = false;
        this.emit('shutdown');
        
        console.log('🛑 Système mémoire thermique arrêté');
    }
}

module.exports = RealThermalMemorySystem;
